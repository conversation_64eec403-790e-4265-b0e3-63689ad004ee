const express = require('express');
const cors = require('cors');
const routes = require('./routes/routes'); // Legacy routes
const responseFormatter = require('./middleware/responseMiddleware');
const errorHandler = require('./middleware/errorHandler');
// const securityMiddleware = require('./middleware/securityMiddleware');

 const helmet = require('helmet');
const path = require('path');

// Load environment variables
require('dotenv').config();

const app = express();

// Trust proxy for secure cookies and rate limiting behind load balancers
app.set('trust proxy', 1);


// CORS configuration with more specific options
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
  credentials: true,
  maxAge: 86400, // 24 hours
  preflightContinue: false,
  optionsSuccessStatus: 204
};

// Apply CORS middleware
app.use(cors(corsOptions));

// Handle preflight requests explicitly
// app.options('/*', cors(corsOptions));

// Body parsers
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));
app.use('/uploads',
  helmet.contentSecurityPolicy({
    directives: {
      defaultSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'blob:'],
    },
  }),
  express.static(path.join(__dirname, 'uploads'), {
    maxAge: '1d',
    etag: true,
    lastModified: true
  })
);


// Response formatter
app.use(responseFormatter);

// Static files with security headers


app.use('/v1/api', routes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    status: 'fail',
    message: 'Route not found',
    success: false,
    data: null
  });
});

// Unhandled errors
process.on('unhandledRejection', (err) => {
  console.error('UNHANDLED REJECTION! 💥 Shutting down...');
  console.error(err.name, err.message);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  console.error(err.name, err.message);
  process.exit(1);
});

module.exports = app;