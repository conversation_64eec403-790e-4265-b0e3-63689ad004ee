const sql = require('mssql');
const { InternalServerError } = require('../utils/errors');
require('dotenv').config();

// Base configuration with security best practices
const baseConfig = {
  options: {
  encrypt: true,
  trustServerCertificate: true, // Allow self-signed certs (INSECURE for public environments!)
  enableArithAbort: true,
  useUTC: true,
  connectTimeout: 60000,
  requestTimeoutcm: 60000
},

  pool: {
    max: 20,
    min: 2, // Keep at least one connection in the pool
    idleTimeoutMillis: 300000 // Increase idle timeout to 5 minutes
  }
};

// Primary database config
const config = {
  ...baseConfig,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
};


// Create connection pools
const pool = new sql.ConnectionPool(config);

// Connection status flag
let isConnected = false;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
const RECONNECT_INTERVAL = 5000; // 5 seconds

// Connection error handling
const handleConnectionError = (poolName, err) => {
  console.error(`Error connecting to SQL Server (${poolName}):`, err);
  // Don't expose sensitive error details in production
  if (process.env.NODE_ENV === 'production') {
    throw new InternalServerError('Database connection error');
  } else {
    throw err;
  }
};

// Function to connect to the database
const connectToDatabase = async () => {
  try {
    if (!isConnected) {
      await pool.connect();
      isConnected = true;
      reconnectAttempts = 0;
      console.log('Connected to SQL Server successfully');
    }
  } catch (err) {
    isConnected = false;
    console.error('Failed to connect to SQL Server:', err);

    // Implement reconnection logic
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;
      console.log(`Reconnect attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS} in ${RECONNECT_INTERVAL}ms`);
      setTimeout(connectToDatabase, RECONNECT_INTERVAL);
    } else {
      console.error('Max reconnection attempts reached. Giving up.');
      handleConnectionError('primary', err);
    }
  }
};

// Connect to primary database
connectToDatabase();

// Error event handlers
pool.on('error', err => {
  console.error('Primary SQL Server connection error:', err);
  isConnected = false;

  // Try to reconnect on error
  if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
    reconnectAttempts++;
    console.log(`Reconnect attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS} in ${RECONNECT_INTERVAL}ms`);
    setTimeout(connectToDatabase, RECONNECT_INTERVAL);
  }
});


// Graceful shutdown function
const closeAllPools = async () => {
  try {
    await pool.close();

    console.log('All database connections closed');
  } catch (err) {
    console.error('Error closing database connections:', err);
  }
};

// Handle application shutdown
process.on('SIGINT', async () => {
  console.log('Application terminating, closing database connections');
  await closeAllPools();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Application terminating, closing database connections');
  await closeAllPools();
  process.exit(0);
});

// Function to ensure the connection is active before executing queries
const ensureConnection = async () => {
  if (!isConnected || !pool.connected) {
    console.log('Database connection is not active. Attempting to reconnect...');
    await connectToDatabase();

    // If still not connected after reconnect attempt, throw error
    if (!isConnected || !pool.connected) {
      throw new Error('Unable to establish database connection');
    }
  }
  return pool;
};

module.exports = {
  pool,
  closeAllPools,
  ensureConnection
};
