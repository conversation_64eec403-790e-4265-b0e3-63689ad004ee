const adsService = require("../services/ads.service");
const sendNotification = require("../services/sendNotficationService");

class AdsController {
  async createAd(req, res, next) {
    try {
      const adData = { ...req.body };
      if (req.file) {
        adData.image = req.file.path;
      }
      const userId = req.user._id;
      const ad = await adsService.createAd(adData, userId);
      const { title, description } = adData;
      const MAX_TITLE_LENGTH = 60;
      const MAX_BODY_LENGTH = 150;

      const notification = {
        title:
          title.length > MAX_TITLE_LENGTH
            ? title.slice(0, MAX_TITLE_LENGTH - 3) + "..."
            : title,
        body:
          description.length > MAX_BODY_LENGTH
            ? description.slice(0, MAX_BODY_LENGTH - 3) + "..."
            : description,
      };

      await sendNotification.sendToAllViaTopic(
        notification.title,
        notification.body
      );
      res.status(201).json(ad);
    } catch (error) {
      next(error);
    }
  }

  async getAds(req, res, next) {
    try {
      const { page, limit } = req.query;
      const ads = await adsService.getAds(
        parseInt(page, 10),
        parseInt(limit, 10)
      );
      res.json(ads);
    } catch (error) {
      next(error);
    }
  }

  async getAdById(req, res, next) {
    try {
      const ad = await adsService.getAdById(req.params.id);
      if (ad) {
        res.json(ad);
      } else {
        res.status(404).json({ message: "Ad not found" });
      }
    } catch (error) {
      next(error);
    }
  }

  async getActiveAds(req, res, next) {
    try {
      const ad = await adsService.getActiveAds();
      res.success(ad, "success");
    } catch (error) {
      next(error);
    }
  }

  async updateAd(req, res, next) {
    try {
      const adData = { ...req.body };
      if (req.file) {
        adData.image = req.file.path;
      }
      const success = await adsService.updateAd(req.params.id, adData);
      if (success) {
        res.json({ message: "Ad updated successfully" });
        const notification = {
          title: "New updated Ad",
          body: `New updated Ad`,
        };
        await sendNotification.sendToAllViaTopic(
          notification.title,
          notification.body
        );
      } else {
        res.status(404).json({ message: "Ad not found" });
      }
    } catch (error) {
      next(error);
    }
  }

  async deleteAd(req, res, next) {
    try {
      const result = await adsService.deleteAd(req.params.id);
      if (result) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Ad not found" });
      }
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AdsController();
