const authService = require("../services/authService");
const login = async (req, res, next) => {
  try {
    const data = await authService.login(req.body);
    res.success(data, "success");
  } catch (error) {
    next(error);
  }
};


const register = async (req, res, next) => {
  try {
    const body = req.body;
    await authService.register(body);
    res.success(null, "Successfully registered!");
  } catch (error) {
    next(error);
  }
};

const getUsers = async (req, res, next) => {
  try {
    const users = await authService.getUsers();
    res.success(users, "success");
  } catch (error) {
    next(error);
  }
};

// const getUserById = async (req, res, next) => {
//   try {
//     const id = req.user.id;
//     const users = await authService.getUserById(id);
//     res.success(users, "success");
//   } catch (error) {
//     next(error);
//   }
// };

const updateUser = async (req, res, next) => {
  try {
    const id = req.params.id;
    const userData = req.body;
    await authService.updateUser(userData, id);
    res.success(null, "Successfully updated!");
  } catch (error) {
    next(error);
  }
};

const activeUser = async (req, res, next) => {
  try {
    const id = req.params.id;
    await authService.activeUser(id);
    res.success(null, "Successfully updated!");
  } catch (error) {
    next(error);
  }
};

const deActiveUser = async (req, res, next) => {
  try {
    const id = req.params.id;
    await authService.deActiveUser(id);
    res.success(null, "Successfully updated!");
  } catch (error) {
    next(error);
  }
};


const changePassword = async (req, res, next) => {
  try {
    const id = req.user.id;
    const { currentPassword, newPassword } = req.body;
    await authService.changePassword(id, currentPassword, newPassword);
    res.success(null, "Successfully updated!");
  } catch (error) {
    next(error);
  }
};
const getUserById = async (req, res, next) => {
  try {
    const id = req.user.id;
    const user = await authService.getUserById(id);
    res.success(user, "success");
  } catch (error) {
    next(error);
  }
}

const changeUsersPassword = async (req, res, next) => {
  try {
   
    const { newPassword, userId } = req.body;
    await authService.changeUserPassword (userId,newPassword);
    res.success(null, "Successfully updated!");
  }catch (error) {
    next(error);
  }
}



module.exports = {
  login,
  register,
  getUsers,
  updateUser,
  deActiveUser,
  activeUser,
  getUserById,
  changePassword,
  changeUsersPassword
};
