// controllers/dashboard.controller.js
const dashboardService = require('../services/dashboard.service');
const logger = require('../logger');

/**
 * Get total number of available devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTotalDevices = async (req, res, next) => {
  try {
    const data = await dashboardService.getTotalDevices();
    res.success(data, 'Total devices count retrieved successfully');
  } catch (err) {
    logger.error('Error in getTotalDevices controller:', err);
    next(err);
  }
};

/**
 * Get count of active and offline devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getDeviceStatusCounts = async (req, res, next) => {
  try {
    const data = await dashboardService.getDeviceStatusCounts();
    res.success(data, 'Device status counts retrieved successfully');
  } catch (err) {
    logger.error('Error in getDeviceStatusCounts controller:', err);
    next(err);
  }
};

/**
 * Get total number of online and offline content across all devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getContentStatusCounts = async (req, res, next) => {
  try {
    const data = await dashboardService.getContentStatusCounts();
    res.success(data, 'Content status counts retrieved successfully');
  } catch (err) {
    logger.error('Error in getContentStatusCounts controller:', err);
    next(err);
  }
};

/**
 * Get count of online content per device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getContentCountPerDevice = async (req, res, next) => {
  try {
    const data = await dashboardService.getContentCountPerDevice();
    res.success(data, 'Content count per device retrieved successfully');
  } catch (err) {
    logger.error('Error in getContentCountPerDevice controller:', err);
    next(err);
  }
};

/**
 * Get count of video and image content per device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getContentTypeCountPerDevice = async (req, res, next) => {
  try {
    const data = await dashboardService.getContentTypeCountPerDevice();
    res.success(data, 'Content type count per device retrieved successfully');
  } catch (err) {
    logger.error('Error in getContentTypeCountPerDevice controller:', err);
    next(err);
  }
};

module.exports = {
  getTotalDevices,
  getDeviceStatusCounts,
  getContentStatusCounts,
  getContentCountPerDevice,
  getContentTypeCountPerDevice
};
