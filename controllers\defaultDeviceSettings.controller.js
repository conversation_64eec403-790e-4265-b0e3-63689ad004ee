const service = require('../services/defaultDeviceSettings.service');
const logger = require('../logger');

/**
 * Get all device settings
 */
const getAll = async (req, res, next) => {
  try {
    const settings = await service.getAllSettings();
    res.success(settings, 'Settings retrieved successfully');
  } catch (err) {
    logger.error('Error in getAll controller:', err);
    next(err);
  }
};

/**
 * Get setting by ID
 */
const getById = async (req, res, next) => {
  try {
    const setting = await service.getSettingById(req.params.id);
    if (!setting) {
      return res.status(404).json({ message: 'Setting not found' });
    }
    res.success(setting, 'Setting retrieved successfully');
  } catch (err) {
    logger.error('Error in getById controller:', err);
    next(err);
  }
};

/**
 * Get settings by device ID
 */
const getByDeviceId = async (req, res, next) => {
  try {
    const settings = await service.getSettingsByDeviceId(req.params.deviceId);
    res.success(settings, 'Settings retrieved successfully');
  } catch (err) {
    logger.error('Error in getByDeviceId controller:', err);
    next(err);
  }
};

/**
 * Get settings by user ID
 */
const getByUserId = async (req, res, next) => {
  try {
    // Use the authenticated user's ID if no ID is provided
    const userId = req.params.userId || req.user.id;
    const settings = await service.getSettingsByUserId(userId);
    res.success(settings, 'Settings retrieved successfully');
  } catch (err) {
    logger.error('Error in getByUserId controller:', err);
    next(err);
  }
};

/**
 * Create a new setting
 */
const create = async (req, res, next) => {
  try {
    // Handle file upload if present
    const filePath = req.file ? `/uploads/${req.file.filename}` : null;

    // Prepare data object
    const data = {
      ...req.body,
      userId: req.user.id,
      filePath
    };

    const result = await service.createSetting(data);
    res.success(result, 'Setting created successfully');
  } catch (err) {
    logger.error('Error in create controller:', err);
    next(err);
  }
};

/**
 * Update a setting
 */
const update = async (req, res, next) => {
  try {
    // Get the existing setting to check if it exists
    const existingSetting = await service.getSettingById(req.params.id);
    if (!existingSetting) {
      return res.status(404).json({ message: 'Setting not found' });
    }

    // Handle file upload if present
    const filePath = req.file ? `/uploads/${req.file.filename}` : undefined;

    // Prepare data object
    const data = {
      ...req.body,
      ...(filePath && { filePath })
    };

    await service.updateSetting(req.params.id, data);
    res.success(null, 'Setting updated successfully');
  } catch (err) {
    logger.error('Error in update controller:', err);
    next(err);
  }
};

/**
 * Delete a setting
 */
const remove = async (req, res, next) => {
  try {
    // Check if the setting exists
    const existingSetting = await service.getSettingById(req.params.id);
    if (!existingSetting) {
      return res.status(404).json({ message: 'Setting not found' });
    }

    await service.deleteSetting(req.params.id);
    res.success(null, 'Setting deleted successfully');
  } catch (err) {
    logger.error('Error in remove controller:', err);
    next(err);
  }
};

module.exports = {
  getAll,
  getById,
  getByDeviceId,
  getByUserId,
  create,
  update,
  remove
};
