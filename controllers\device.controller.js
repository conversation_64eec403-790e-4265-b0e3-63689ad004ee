const service = require('../services/device.service');
const { broadcastSettingsToDevice } = require('../socket');
const serviceSettings = require('../services/defaultDeviceSettings.service');

const getAll = async (req, res,next) => {
  try {
    const devices = await service.getAllDevices();
    res.success(devices,'success');
  } catch (err) {
    next(err);
  }
};

const getById = async (req, res,next) => {
  try {
    const device = await service.getDeviceById(req.params.id);
    if (!device) return res.status(404).json({ message: 'Not found' });
    res.success(devices,'success');
  } catch (err) {
    next(err);
  }
};

const create = async (req, res,next) => {
  try {
    const device = {deviceId:req.body.deviceId,status:1};
    const existingDevice = await service.findDeviceByDeviceId(device.deviceId);
   
    if (!existingDevice) {
      const deviceId= await service.createDevice(device);
      const setting  =  await serviceSettings.getDefaultSetting();
      const data = {
        device: deviceId,
        userId:0,
        default_Id: setting.id
      }
    
     await serviceSettings.createSetting(data);
      res.success(null,'Device created');
    }
  } catch (err) {
    next(err);
  }
};

const settingsDevice = async (req, res,next) =>{
  try {
    const settings =  req.body;
    let data = {};
    if(settings.type  === 'video'){
     const filePath = req.file ? `/uploads/${req.file.filename}` : null;

    if (!req.file) {

      const err = new Error(`File is required`);
      err.statusCode = 400;
      throw err;
    }

    const mimeType = req.file.mimetype;
    let fileType = null;

     if (mimeType.startsWith('video/')) {
      fileType = 'video';
    } else {
      const err = new Error(`Only image or video files are allowed`);
      err.statusCode = 400;
      throw err;
    }

    data = {
      deviceId: settings.deviceId,
      default_settings:{
        name:settings.name,
        type:'video',
        filePath: filePath
      }
    }
  }else{
    data = {
      deviceId: settings.deviceId,
      default_settings:{
        name:settings.name,
        type:'animation',
      }
    }
  }

   await service.deviceSettings(data);

   // Broadcast the settings update to the device
   if (settings.deviceId) {
     await broadcastSettingsToDevice(settings.deviceId);
   }

   res.success(null,'Device settings updated');

  } catch (error) {
     next(error);
  }
}



const update = async (req, res,next) => {
  try {
    await service.updateDevice(req.params.id, req.body);
    res.success(null,'Device updated');
  } catch (err) {
    next(err);
  }
};

const remove = async (req, res,next) => {
  try {
    await service.deleteDevice(req.params.id);
    res.success(null,'Device deleted');
  } catch (err) {
    next(err);
  }
};

const findDefault = async (req, res,next) => {
  try {
      const device = await service.findDefault();
      console.log(device);
      res.success(device,'success');
  } catch (error) {
    next(error);
  }

}


module.exports = {
  getAll,
  getById,
  create,
  update,
  remove,
  settingsDevice,
  findDefault
};
