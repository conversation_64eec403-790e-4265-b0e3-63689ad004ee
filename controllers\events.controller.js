const eventsService = require("../services/events.service");
const sendNotification = require("../services/sendNotficationService");

class EventsController {
  async createEvent(req, res, next) {
    try {
      const eventData = { ...req.body };
      const userId = req.user._id;
      const event = await eventsService.createEvent(eventData, userId);
      const { title, desciption } = eventData;
      const MAX_TITLE_LENGTH = 60;
      const MAX_BODY_LENGTH = 150;

      const notification = {
        title:
          title && title.length > MAX_TITLE_LENGTH
            ? title.slice(0, MAX_TITLE_LENGTH - 3) + "..."
            : title || "New Event",
        body:
          desciption && desciption.length > MAX_BODY_LENGTH
            ? desciption.slice(0, MAX_BODY_LENGTH - 3) + "..."
            : desciption || "A new event has been created",
      };

      await sendNotification.sendToAllViaTopic(
        notification.title,
        notification.body
      );
      res.status(201).json(event);
    } catch (error) {
      next(error);
    }
  }

  async getEvents(req, res, next) {
    try {
      const { page, limit } = req.query;
      const events = await eventsService.getEvents(
        parseInt(page, 10),
        parseInt(limit, 10)
      );
      res.json(events);
    } catch (error) {
      next(error);
    }
  }

  async getEventById(req, res, next) {
    try {
      const event = await eventsService.getEventById(req.params.id);
      if (event) {
        res.json(event);
      } else {
        res.status(404).json({ message: "Event not found" });
      }
    } catch (error) {
      next(error);
    }
  }

  async getActiveEvents(req, res, next) {
    try {
      const events = await eventsService.getActiveEvents();
      res.success(events, "success");
    } catch (error) {
      next(error);
    }
  }

  async updateEvent(req, res, next) {
    try {
      const eventData = { ...req.body };
      const success = await eventsService.updateEvent(req.params.id, eventData);
      if (success) {
        res.json({ message: "Event updated successfully" });
        const { title, desciption } = eventData;
        const MAX_TITLE_LENGTH = 60;
        const MAX_BODY_LENGTH = 150;

        const notification = {
          title:
            title && title.length > MAX_TITLE_LENGTH
              ? title.slice(0, MAX_TITLE_LENGTH - 3) + "..."
              : title || "Event Updated",
          body:
            desciption && desciption.length > MAX_BODY_LENGTH
              ? desciption.slice(0, MAX_BODY_LENGTH - 3) + "..."
              : desciption || "An event has been updated",
        };
        await sendNotification.sendToAllViaTopic(
          notification.title,
          notification.body
        );
      } else {
        res.status(404).json({ message: "Event not found" });
      }
    } catch (error) {
      next(error);
    }
  }

  async deleteEvent(req, res, next) {
    try {
      const result = await eventsService.deleteEvent(req.params.id);
      if (result) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Event not found" });
      }
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new EventsController();
