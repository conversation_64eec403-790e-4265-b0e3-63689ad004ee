const permissionService = require('../services/permissionService');

/**
 * Get all permissions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllPermissions = async (req, res, next) => {
  try {
    const permissions = await permissionService.getAllPermissions();
    res.success(permissions, 'Permissions retrieved successfully');
  } catch (error) {
    next(error);
  }
};



/**
 * Get permission by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getPermissionById = async (req, res, next) => {
  try {
    const permission = await permissionService.getPermissionById(req.params.id);
    res.success(permission, 'Permission retrieved successfully');
  } catch (error) {
    next(error);
  }
};

/**
 * Get permissions by user ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getPermissionsByUserId = async (req, res, next) => {
  try {
    const permissions = await permissionService.getPermissionsByUserId(req.params.userId);
    res.success(permissions, 'Permissions retrieved successfully');
  } catch (error) {
    next(error);
  }
};

/**
 * Get permissions by ower ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getPermissionsByOwerId = async (req, res, next) => {
  try {
    
    
    // Convert req.user.id to an integer to ensure it's a valid number
    const userId = parseInt(req.user.id, 10);
    if (isNaN(userId)) {
      const err = new Error('Invalid user ID');
      err.statusCode = 400;
      throw err;
    }
    const permissions = await permissionService.getPermissionsByUserId(userId);
    res.success(permissions, 'Permissions retrieved successfully');
  } catch (error) {
    next(error);
  }
};
/**
 * Get permissions by menu ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getPermissionsByMenuId = async (req, res, next) => {
  try {
    const permissions = await permissionService.getPermissionsByMenuId(req.params.menuId);
    res.success(permissions, 'Permissions retrieved successfully');
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new permission
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createPermission = async (req, res, next) => {
  try {
    const result = await permissionService.createPermission(req.body);
    res.success(result, 'Permission created successfully');
  } catch (error) {
    next(error);
  }
};

/**
 * Update an existing permission
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updatePermission = async (req, res, next) => {
  try {
    const permission = await permissionService.updatePermission(req.params.id, req.body);
    res.success(permission, 'Permission updated successfully');
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a permission by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deletePermission = async (req, res, next) => {
  try {
    await permissionService.deletePermission(req.params.id);
    res.success(null, 'Permission deleted successfully');
  } catch (error) {
    next(error);
  }
};

/**
 * Delete permissions by user ID and menu ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deletePermissionByUserAndMenu = async (req, res, next) => {
  try {
    const { userId, menuId } = req.params;
    await permissionService.deletePermissionByUserAndMenu(userId, menuId);
    res.success(null, 'Permission deleted successfully');
  } catch (error) {
    next(error);
  }
};

/**
 * Create multiple permissions for a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createMultiplePermissions = async (req, res, next) => {
  try {
    await permissionService.createMultiplePermissions(req.body);
    res.success(null, 'Permissions created successfully');
  } catch (error) {
    next(error);
  }
};


module.exports = {
  getAllPermissions,
  getPermissionById,
  getPermissionsByUserId,
  getPermissionsByMenuId,
  createPermission,
  updatePermission,
  deletePermission,
  deletePermissionByUserAndMenu,
  createMultiplePermissions,
  getPermissionsByOwerId
};
