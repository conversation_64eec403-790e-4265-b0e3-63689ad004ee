const service = require('../services/screenPost.service');
const deviceService = require('../services/device.service');
const { broadcastToDevice } = require('../socket');
const sendNotification = require('../services/sendNotficationService');


const getAll = async (req, res,next) => {
  try {
    const posts = await service.getAllScreenPosts();
    res.success(posts,'success');
  } catch (err) {
   next(err);
  }
};

const getPostsByUserId = async (req, res,next) => {
  try {
    const userId = req.user.id;
    const posts = await service.getScreenPostsByUserId(userId);
    res.success(posts,'success');
  } catch (err) {
   next(err);
  }
};

const getById = async (req, res,next) => {
  try {
    const post = await service.getScreenPostById(req.params.id);
    if (!post) return res.status(404).json({ message: 'Not found' });
    res.success(post,'success');
  } catch (err) {
   next(err);
  }
};

const postToScreen  = async (req, res, next) =>{
  try {
    // Get the post first to get the deviceId
    const postData = await service.getScreenPostById(req.params.id);
    if (!postData) return res.status(404).json({ message: 'Not found' });

    // Update the post status
    const post = await service.postToScreen(req.params.id);

    // Get the device to get the deviceId string
    const device = await deviceService.getDeviceById(postData.deviceId);
    if (device && device.deviceId) {
      // Broadcast the update to all connected instances of this device
      await broadcastToDevice(device.deviceId);
    }

     const notification = {
      title: 'New Notice Post',
      body: `There is a new notice post posted`,
    };
    await sendNotification.sendToAllViaTopic(notification.title, notification.body);

    res.success(post,'success');
  } catch (err) {
   next(err);
  }
}

const removePost = async (req, res, next) =>{
  try {
    // Get the post first to get the deviceId
    
    const postData = await service.getScreenPostById(req.params.id);
    if (!postData) return res.status(404).json({ message: 'Not found' });
   
    
    const post = await service.removePost(req.params.id);

    // Get the device to get the deviceId string
    const device = await deviceService.getDeviceById(postData.deviceId);
    if (device && device.deviceId) {
      // Broadcast the update to all connected instances of this device
      await broadcastToDevice(device.deviceId);
    }

    res.success(post,'success');
  } catch (err) {
   next(err);
  }
}

const create = async (req, res, next) => {
  try {
    const filePath = req.file ? `/uploads/${req.file.filename}` : null;

    if (!req.file) {

      const err = new Error(`File is required`);
      err.statusCode = 400;
      throw err;
    }

    const mimeType = req.file.mimetype;
    let fileType = null;

    if (mimeType.startsWith('image/')) {
      fileType = 'image';
    } else if (mimeType.startsWith('video/')) {
      fileType = 'video';
    } else {
      const err = new Error(`Only image or video files are allowed`);
      err.statusCode = 400;
      throw err;
    }

    const data = {
      ...req.body,
      filePath,
      fileType,
      status: 0,
      userId: req.user.id
    };

    await service.createScreenPost(data);
   
    res.success(null,'Created successfully');

  } catch (error) {
    next(error);
  }
};

const update = async (req, res, next) => {
  try {
    const filePath = req.file ? `/uploads/${req.file.filename}` : null;
    const fileType = req.file ? req.file.mimetype : null;

    const data = {
      ...req.body,
      ...(filePath && { filePath }),
      ...(fileType && { fileType }),
    };

    await service.updateScreenPost(req.params.id, data);
    res.success(null, 'Updated successfully');
  } catch (error) {
    next(error);
  }
};



const remove = async (req, res,next) => {
  try {
    await service.deleteScreenPost(req.params.id);
    res.success(null,'Deleted successfully');
  } catch (err) {
   next(err);
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  remove,
  postToScreen,
  removePost,
  getPostsByUserId
};
