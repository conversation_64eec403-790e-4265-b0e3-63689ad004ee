
const settingsService = require('../services/settingsService');

const toGregorian = async (req, res) => {
   try {
    const year = Number(req.params.year);
    const month = Number(req.params.month);
    const day = Number(req.params.day);
    const gregorian = await settingsService.toGregorian(year, month, day);
   
    res.status(200).json(gregorian);
   } catch (error) {
    res.status(500).json({message: error.message,status:500});
   }
};

const toEthiopian = async (req, res) => {
    try {
     const year = Number(req.params.year);
     const month = Number(req.params.month);
     const day = Number(req.params.day);
     const ethiopian = await settingsService.toEthiopian(year, month, day);
    
     res.status(200).json(ethiopian);
    } catch (error) {
     res.status(500).json({message: error.message,status:500});
    }
 };

module.exports = {
    toGregorian,
    toEthiopian
};