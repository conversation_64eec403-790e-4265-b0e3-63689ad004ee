// logger.js
const winston = require('winston');
const path = require('path');

// Define custom log formats
const logFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), // Add a timestamp
    winston.format.errors({ stack: true }), // Log stack trace for errors
    winston.format.splat(), // Allows passing objects/multiple arguments
    winston.format.json() // Output log entry as JSON
    // Or use a simple format for console readability:
    // winston.format.printf(({ level, message, timestamp, stack }) => {
    //     return `${timestamp} ${level.toUpperCase()}: ${message}${stack ? '\n' + stack : ''}`;
    // })
);

// Determine log level based on environment (e.g., 'debug' in dev, 'info' in prod)
const logLevel = process.env.NODE_ENV === 'development' ? 'debug' : 'info';

// Define transports (where logs go)
const transports = [
    // Console Transport (logs to terminal)
    new winston.transports.Console({
        format: winston.format.combine(
            winston.format.colorize(), // Add colors to console output
            // Use the simple printf format for console readability
            winston.format.printf(({ level, message, timestamp, stack }) => {
                 return `${timestamp} ${level.toUpperCase()}: ${message}${stack ? '\n' + stack : ''}`;
             })
        )
    }),
    // File Transport (logs to a file)
    // You might want different files for different levels (e.g., errors.log)
    new winston.transports.File({
        filename: path.join(__dirname, '..', 'logs', 'app.log'), // Log file location
        level: logLevel, // Log level for this file
        format: logFormat // Use the JSON format for file logs
    }),
     // Optional: File transport specifically for errors
    new winston.transports.File({
        filename: path.join(__dirname, '..', 'logs', 'errors.log'),
        level: 'error', // Only log errors to this file
        format: logFormat
    })
];

// Create the logger instance
const logger = winston.createLogger({
    level: logLevel, // Default log level for the logger
    levels: winston.config.npm.levels, // Use standard npm log levels (error, warn, info, http, verbose, debug, silly)
    format: logFormat, // Default format (can be overridden per transport)
    transports: transports, // List of transports
    exitOnError: false, // Do not exit on handled exceptions
});

// If not in production, also log to a debug file
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.File({
        filename: path.join(__dirname, '..', 'logs', 'debug.log'),
        level: 'debug',
        format: logFormat
    }));
}


// Helper to log unhandled rejections (optional, but good practice)
// You're already catching this in server.js, but winston can log it too.
// logger.exceptions.handle(
//     new winston.transports.File({ filename: 'logs/exceptions.log', format: logFormat })
// );


module.exports = logger;