const { pool } = require("../config/db");
const sql = require("mssql");

/**
 * Get all permissions
 * @returns {Promise<Array>} Array of all permissions
 */
const getAllPermissions = async () => {
  const result = await pool.request().query(`
    SELECT * FROM tbl_permissions
    ORDER BY id DESC
  `);
  return result.recordset;
};

/**
 * Get permission by ID
 * @param {number} id - Permission ID
 * @returns {Promise<Object>} Permission object
 */
const getPermissionById = async (id) => {
  const result = await pool.request()
    .input('id', sql.Int, id)
    .query('SELECT * FROM tbl_permissions WHERE id = @id');
  return result.recordset[0];
};

/**
 * Get permissions by user ID
 * @param {number} userId - User ID
 * @returns {Promise<Array>} Array of permissions for the user
 */
const getPermissionsByUserId = async (userId) => {
  const request = await pool.request();
  request.input("userId", sql.Int, userId);
  const result = await request.query(`
    SELECT * FROM tbl_permissions WHERE userId = @userId
  `);
  return result.recordset;
};

/**
 * Get permissions by menu ID
 * @param {string} navMenuId - Navigation menu ID
 * @returns {Promise<Array>} Array of permissions for the menu
 */
const getPermissionsByMenuId = async (navMenuId) => {
  const request = await pool.request();
  request.input("navMenuId", sql.NVarChar, navMenuId);
  const result = await request.query(`
    SELECT * FROM tbl_permissions WHERE nav_menuId = @navMenuId
  `);
  return result.recordset;
};

/**
 * Create a new permission
 * @param {Object} permissionData - Permission data object
 * @returns {Promise<Object>} Result of the insert operation
 */
const createPermission = async (permissionData) => {
  const request = await pool.request();
  request.input("userId", sql.Int, permissionData.userId);
  request.input("nav_menuId", sql.NVarChar, permissionData.nav_menuId);
  
  const result = await request.query(`
    INSERT INTO tbl_permissions (userId, nav_menuId) 
    VALUES (@userId, @nav_menuId);
    SELECT SCOPE_IDENTITY() AS id;
  `);
  
  return result.recordset[0];
};

/**
 * 
 * @param {*} userId 
 * @returns 
 */
const deletePermissionByUserId = async (userId) => {
  const request = await pool.request();
  request.input("userId", sql.Int, userId);
  const result = await request.query(`
    DELETE FROM tbl_permissions WHERE userId = @userId
  `);
  return result.recordset[0];
};


/**
 * Update an existing permission
 * @param {number} id - Permission ID
 * @param {Object} permissionData - Permission data object
 * @returns {Promise<Object>} Result of the update operation
 */
const updatePermission = async (id, permissionData) => {
  const request = await pool.request();
  request.input("id", sql.Int, id);
  request.input("userId", sql.Int, permissionData.userId);
  request.input("nav_menuId", sql.NVarChar, permissionData.nav_menuId);
  
  const result = await request.query(`
    UPDATE tbl_permissions 
    SET userId = @userId, nav_menuId = @nav_menuId
    WHERE id = @id
  `);
  
  return result;
};

/**
 * Delete a permission by ID
 * @param {number} id - Permission ID
 * @returns {Promise<Object>} Result of the delete operation
 */
const deletePermission = async (id) => {
  const request = await pool.request();
  request.input("id", sql.Int, id);
  
  const result = await request.query(`
    DELETE FROM tbl_permissions WHERE id = @id
  `);
  
  return result;
};

/**
 * Delete permissions by user ID and menu ID
 * @param {number} userId - User ID
 * @param {string} menuId - Menu ID
 * @returns {Promise<Object>} Result of the delete operation
 */
const deletePermissionByUserAndMenu = async (userId, menuId) => {
  const request = await pool.request();
  request.input("userId", sql.Int, userId);
  request.input("menuId", sql.NVarChar, menuId);
  
  const result = await request.query(`
    DELETE FROM tbl_permissions 
    WHERE userId = @userId AND nav_menuId = @menuId
  `);
  
  return result;
};

module.exports = {
  getAllPermissions,
  getPermissionById,
  getPermissionsByUserId,
  getPermissionsByMenuId,
  createPermission,
  updatePermission,
  deletePermission,
  deletePermissionByUserAndMenu,
  deletePermissionByUserId
};
