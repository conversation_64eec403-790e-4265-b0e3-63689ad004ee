const { pool } = require("../config/db");
const sql = require("mssql");

const getUserByUsername = async (username) => {
  const result = await pool
    .request()
    .input("username", sql.NVarChar, username) // Bind the @username parameter
    .query("SELECT * FROM tbl_users WHERE username = @username");

  return result.recordset[0];
};

const getUserAll = async () => {
  const result = await pool
    .request()
    .query("SELECT * FROM tbl_users");

  return result.recordset;
};


const getUsers = async () => {
  const result = await pool.request()
    .query(`SELECT u.id,u.username,u.fullName,u.createdAt,u.updatedAt,u.status
           FROM tbl_users as u  
      `);
  return result.recordset;
};

const getUserById = async (id) => {
  const result = await pool.request()
    .input("id", sql.Int, id)
    .query(`SELECT u.id,u.username,u.fullName,u.createdAt,u.updatedAt,u.status
       FROM tbl_users as u
       where u.id = @id
      `);
  return result.recordset[0];
};

const findUserById = async (id) =>{
  const result = await pool.request()
  .input("id", sql.Int, id)
  .query(`SELECT *  
    FROM tbl_users 
    where id = @id
    `);
   return result.recordset[0];
}


const createUser = async (userData) => {
  const request = await pool.request();
  // Bind each field from userData to the request
  request.input("username", sql.NVarChar, userData.username);
  request.input("fullName", sql.NVarChar, userData.fullName);
  request.input("password", sql.NVarChar, userData.password);
  request.input("createdAt", sql.DateTime, userData.createdAt);
  request.input("status", sql.Int, userData.status);

  const result = await request.query(`
          INSERT INTO tbl_users (username,fullName,password,createdAt,status) 
          VALUES (@username,@fullName,@password,@createdAt,@status);
      `);

  return result;
};

const updateUser = async (userData, id) => {
  const request = await pool.request();
  // Bind each field from userData to the request
  request.input("fullName", sql.NVarChar, userData.fullName);
  request.input("updatedAt", sql.DateTime, userData.updatedAt);
  request.input("id", sql.Int, id);

  await request.query(`
        update  gr_users SET fullName=@fullName,updatedAt=@updatedAt
        WHERE id=@id
    `);
};



const changeStatus = async (status, id) => {
  const request = await pool.request();
  request.input("status", sql.Int, status);
  request.input("id", sql.Int, id);
  await request.query(`
    update  tbl_users SET status=@status
    WHERE id=@id
`);
};



 const resetPassword = async(userId,password)=>{
  const request = await pool.request();
  request.input("userId", sql.Int, userId);
  request.input("password", sql.NVarChar, password);
  request.input("updatedAt", sql.DateTime, new Date());
  await request.query(`
     UPDATE tbl_users set password=@password,updatedAt=@updatedAt Where id=@userId
   `);
 }


module.exports = {
  getUserByUsername,
  createUser,
  getUsers,
  updateUser,
  changeStatus,
  getUserById,
  resetPassword,
  findUserById,
  getUserAll,

};
