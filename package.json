{"name": "smart_screen_control", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "author": "<PERSON><PERSON><PERSON> wube", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "ethiopian-date": "^0.0.6", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "helmet": "^7.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mssql": "^11.0.1", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "node-schedule": "^2.1.1", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "winston": "^3.17.0", "xlsx": "^0.18.5", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/node-schedule": "^2.1.7", "axios": "^1.9.0"}}