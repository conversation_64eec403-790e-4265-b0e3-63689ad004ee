const express = require('express');
const router = express.Router();
const adsController = require('../controllers/ads.controller');
const multer = require('multer');
const fs = require('fs');

// Setup multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = 'uploads/';

    // Create the directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage });

router.post('/', upload.single('image'), adsController.createAd);
router.get('/', adsController.getAds);
router.get('/:id', adsController.getAdById);
router.put('/:id', upload.single('image'), adsController.updateAd);
router.delete('/:id', adsController.deleteAd);

module.exports = router;