const express = require('express');
const router = express.Router();
const authMiddleware  = require('../middleware/authMiddleware');
const authController = require('../controllers/authController');
const postingController = require('../controllers/screenPost.controller');
const deviceController = require('../controllers/device.controller');
const permissionController = require('../controllers/permissionController');
const adsController = require('../controllers/ads.controller');
const settingsController = require('../controllers/settingsController');
const defaultDeviceSettingsController = require('../controllers/defaultDeviceSettings.controller');
const dashboardController = require('../controllers/dashboard.controller');
const multer = require('multer');
const fs = require('fs');

// Setup multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = 'uploads/';

    // Create the directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage });
/**
 * users routes
 *
 */
router.post('/login',   authController.login);
router.get('/ads/active', adsController.getActiveAds);
router.post('/createaccount', authController.register);
router.get('/users', authMiddleware, authController.getUsers);
router.get('/profile', authMiddleware, authController.getUserById);
router.put('/users/:id', authMiddleware, authController.updateUser);
router.put('/users/active/:id', authMiddleware, authController.activeUser);
router.put('/users/deactive/:id', authMiddleware, authController.deActiveUser);
router.post('/user/change/password', authMiddleware, authController.changePassword);
router.put('/password/resets', authMiddleware, authController.changeUsersPassword);

/**
 * posting routes
 *
 */

router.get('/posting',authMiddleware, postingController.getAll);
router.get('/posting/:id',authMiddleware, postingController.getById);
router.post('/posting', authMiddleware,upload.single('file'), postingController.create);
router.put('/posting/:id', authMiddleware,upload.single('file'), postingController.update);
router.delete('/posting/:id', authMiddleware,postingController.remove);
router.put('/posting/:id/post', authMiddleware,postingController.postToScreen);
router.put('/posting/:id/unpost', authMiddleware,postingController.removePost);
router.get('/postingByUserId',authMiddleware, postingController.getPostsByUserId);


/**
 * device routes
 *
 */

router.get('/devices',authMiddleware ,deviceController.getAll);
router.get('/device/:id',authMiddleware, deviceController.getById);
router.post('/device', deviceController.create);
router.put('/device/:id',authMiddleware, deviceController.update);
router.delete('/device/:id',authMiddleware, deviceController.remove);
router.post('/device/settings',authMiddleware,upload.single('file'), deviceController.settingsDevice);
router.get('/default/settings',authMiddleware, deviceController.findDefault);


/**
 * permission routes
 *
 */
router.get('/permissions', authMiddleware, permissionController.getAllPermissions);
router.get('/permissions/:id', authMiddleware, permissionController.getPermissionById);
router.get('/permissions/user/:userId', authMiddleware, permissionController.getPermissionsByUserId);
router.get('/permissions/menu/:menuId', authMiddleware, permissionController.getPermissionsByMenuId);
router.post('/permissions', authMiddleware, permissionController.createPermission);
router.post('/permissions/multiple', authMiddleware, permissionController.createMultiplePermissions);
router.put('/permissions/:id', authMiddleware, permissionController.updatePermission);
router.delete('/permissions/:id', authMiddleware, permissionController.deletePermission);
router.delete('/permissions/user/:userId/menu/:menuId', authMiddleware, permissionController.deletePermissionByUserAndMenu);
router.get('/owner/permissions', authMiddleware, permissionController.getPermissionsByOwerId);

/**
 * settings routes
 */
router.get('/toGregorian/:year/:month/:day',authMiddleware, settingsController.toGregorian);
router.get('/toEthiopian/:year/:month/:day',authMiddleware, settingsController.toEthiopian);

/**
 * default device settings routes
 */
router.get('/device-settings', authMiddleware, defaultDeviceSettingsController.getAll);
router.get('/device-settings/user', authMiddleware, defaultDeviceSettingsController.getByUserId);
router.get('/device-settings/user/:userId', authMiddleware, defaultDeviceSettingsController.getByUserId);
router.get('/device-settings/device/:deviceId', authMiddleware, defaultDeviceSettingsController.getByDeviceId);
router.get('/device-settings/:id', authMiddleware, defaultDeviceSettingsController.getById);
router.post('/device-settings', authMiddleware, upload.single('file'), defaultDeviceSettingsController.create);
router.put('/device-settings/:id', authMiddleware, upload.single('file'), defaultDeviceSettingsController.update);
router.delete('/device-settings/:id', authMiddleware, defaultDeviceSettingsController.remove);

/**
 * dashboard analytics routes
 */
router.get('/dashboard/devices/total', authMiddleware, dashboardController.getTotalDevices);
router.get('/dashboard/devices/status', authMiddleware, dashboardController.getDeviceStatusCounts);
router.get('/dashboard/content/status', authMiddleware, dashboardController.getContentStatusCounts);
router.get('/dashboard/content/per-device', authMiddleware, dashboardController.getContentCountPerDevice);
router.get('/dashboard/content/types', authMiddleware, dashboardController.getContentTypeCountPerDevice);

/**
 * ads routes
 */
const adsRoutes = require('./ads.routes');
router.use('/ads', authMiddleware, adsRoutes);

module.exports = router;