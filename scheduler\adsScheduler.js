const cron = require('node-cron');
const adsService = require('../services/ads.service');
class AdsScheduler {
  static task = null;

  static init(options = {}) {
    const schedule = '*/20 * * * * *';// Every hour
    console.log(`AdsScheduler: Initializing with schedule "${schedule}"`);
    
    AdsScheduler.task = cron.schedule(schedule, async () => {
      try {
        
        await adsService.updateExpiredAds();
        await adsService.updateNonExpiredAds();
      } catch (error) {
        console.error('AdsScheduler: Error running scheduled task:', error);
      }
    });

    return AdsScheduler.task;
  }

  static stop() {
    if (AdsScheduler.task) {
      AdsScheduler.task.stop();
      console.log('Ads scheduler stopped.');
    }
  }
}

module.exports = AdsScheduler;
