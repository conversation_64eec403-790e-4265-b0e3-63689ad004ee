const cron = require('node-cron');
const PostService = require('../services/screenPost.service');

/**
 * Scheduler to automatically check for expired challenges and end them
 */
class Postscheduler {
  /**
   * Initialize the scheduler
   * @param {Object} options - Scheduler options
   * @param {string} options.schedule - Cron schedule expression (default: every hour)
   */
  static init(options = {}) {
    const schedule =  '0 * * * *'; // Default: Run every hour at minute 0
    
    console.log(`PostingScheduler: Initializing with schedule "${schedule}"`);
    
    // Schedule the task
    const task = cron.schedule(schedule, async () => {
      
      try {
        const result = await PostService.updateScheduledPosts();
      
      } catch (error) {
        console.error('PostingScheduler: Error running scheduled task:', error);
      }
    });
    
   
    return task;
  }
}

module.exports = Postscheduler;
