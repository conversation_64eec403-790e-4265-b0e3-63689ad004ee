const { ensureConnection } = require("../config/db");
const sql = require("mssql");
const ethiopianDate = require("./converter");

class AdsService {
  async createAd(adData, userId) {
    const pool = await ensureConnection();
    const request = pool.request();
    const { image, title, description, expireAt } = adData;

    const result = await request
      .input("image", sql.NVarChar, image)
      .input("title", sql.NVarChar, title)
      .input("description", sql.NVarChar, description)
      .input("expireAt", sql.DateTime, new Date(expireAt))
      .input("postedBy", sql.Int, userId)
      .query(
        "INSERT INTO tbls_ads (image, title, description, expireAt, createdAt, updatedAt, isExpired,postedBy) OUTPUT INSERTED.* VALUES (@image, @title, @description, @expireAt, GETDATE(), GETDATE(), 0,@postedBy)"
      );
    return result.recordset[0];
  }

   async toEthiopian(year, mm, dd) {
    const date = [year, mm, dd];
    const gregorian = ethiopianDate.toEthiopian(date);
    return gregorian;
  };
  async getAds(page = 1, limit = 10) {
    const pool = await ensureConnection();
    const offset = (page - 1) * limit;

    const countResult = await pool
      .request()
      .query("SELECT COUNT(*) as count FROM tbls_ads");
    const totalItems = countResult.recordset[0].count;
    const totalPages = Math.ceil(totalItems / limit);

    const result = await pool
      .request()
      .input("offset", sql.Int, offset)
      .input("limit", sql.Int, limit)
      .query(
        "SELECT * FROM tbls_ads ORDER BY id DESC OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY"
      );

    const ads = await Promise.all(
      result.recordset.map(async (ad) => {
        const expiredAt = new Date(ad.expireAt);
        const ethDate = await this.toEthiopian(
          expiredAt.getFullYear(),
          expiredAt.getMonth() + 1, // JS months are 0-based
          expiredAt.getDate()
        );
       
        return {
          ...ad,
          expiredAtEthiopian: ethDate,
        };
      })
    );

    return {
      data: ads,
      pagination: {
        totalItems,
        totalPages,
        currentPage: page,
        pageSize: limit,
      },
    };
  }

  async getAdById(id) {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .input("id", sql.Int, id)
      .query("SELECT * FROM tbls_ads WHERE id = @id");
    return result.recordset[0];
  }

  async getActiveAds() {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .query(
        "SELECT * FROM tbls_ads WHERE isExpired = 0 AND expireAt > GETDATE() ORDER BY id DESC"
      );
    return result.recordset;
  }

  async updateAd(id, adData) {
    const pool = await ensureConnection();
    const { image, title, description, expireAt } = adData;
    const request = pool.request().input("id", sql.Int, id);

    const setClauses = [];

    if (title !== undefined) {
      setClauses.push("title = @title");
      request.input("title", sql.NVarChar, title);
    }
    if (description !== undefined) {
      setClauses.push("description = @description");
      request.input("description", sql.NVarChar, description);
    }
    if (expireAt !== undefined) {
      setClauses.push("expireAt = @expireAt");
      request.input("expireAt", sql.DateTime, expireAt);
    }
    if (adData.isExpired !== undefined) {
      setClauses.push("isExpired = @isExpired");
      request.input("isExpired", sql.Bit, adData.isExpired);
    }
    if (image !== undefined) {
      setClauses.push("image = @image");
      request.input("image", sql.NVarChar, image);
    }

    if (setClauses.length === 0) {
      return true;
    }

    let query = `UPDATE tbls_ads SET ${setClauses.join(
      ", "
    )}, updatedAt = GETDATE() WHERE id = @id`;

    const result = await request.query(query);
    return result.rowsAffected[0] > 0;
  }

  async updateExpiredAds() {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .query(
        "UPDATE tbls_ads SET isExpired = 1, updatedAt = GETDATE() WHERE expireAt < GETDATE() AND (isExpired = 0 OR isExpired IS NULL)"
      );
    return result.rowsAffected[0];
  }
  async updateNonExpiredAds() {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .query(
        "UPDATE tbls_ads SET isExpired = 0, updatedAt = GETDATE() WHERE expireAt > GETDATE() AND isExpired = 1 "
      );
    return result.rowsAffected[0];
  }

  async deleteAd(id) {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .input("id", sql.Int, id)
      .query("DELETE FROM tbls_ads WHERE id = @id");
    return result.rowsAffected[0] > 0;
  }
}

module.exports = new AdsService();
