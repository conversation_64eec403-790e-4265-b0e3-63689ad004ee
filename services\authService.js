const userModel = require("../models/userModel");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");

require("dotenv").config();
const register = async (userData) => {
  const userbody = {
    username: userData.username,
    fullName: userData.fullName,
    password: userData.password,
    status: 1,
    createdAt: new Date(),
  };

  const user = await userModel.getUserByUsername(userData.username);
  if (user) {
    throw new Error("ተጠቃሚው ከዚህ በፊት ተመዝግቧል!!");
  }

  const hashedPassword = await bcrypt.hash(userbody.password, 8);
  userbody.password = hashedPassword;
  await userModel.createUser(userbody);

  const userDetails = await userModel.getUserByUsername(userData.username);
  return {
    msg: "Successfully registered!!",
    status: 200,
    userId: userDetails.id,
  };
};

const login = async ({ username, password }) => {
  
  const user = await userModel.getUserByUsername(username);
 if(!user){
    const err = new Error(`Account not found! ${username}`);
    err.statusCode = 400;
    throw err;
 }

  if (!user || !(await bcrypt.compare(password, user.password))) {
   
    const err = new Error(`Incorrect password or username`);
    err.statusCode = 400;
    throw err;
  }

  if(user.status === 0){
    const err = new Error(`The Account is Deactivated`);
    err.statusCode = 400;
    throw err;
  }

 
  const token = jwt.sign({ id: user.id }, process.env.JWT_SECRET, {
    expiresIn: "24h",
  });


  return {
    token: token,
    status: 200,
  };
};




const getUsers = async () => {
  const users = await userModel.getUsers();
  return users;
};

const getUserById = async (id) => {
  
  const users = await userModel.getUserById(id);
  
  return users;
};

const updateUser = async (userData, id) => {
  userData["modifiyed_on"] = new Date();
  await userModel.updateUser(userData, id);
};

const activeUser = async (id) => {
  await userModel.changeStatus(1, id);
};

const deActiveUser = async (id) => {
  await userModel.changeStatus(0, id);
};


const changePassword = async (userId,currentPassword,newPassword) => {

  const user = await userModel.findUserById(userId);
  if(!user) {
    const err = new Error(`something went wrong!`);
    err.statusCode = 400;
    throw err;
  }
  
  if (!(await bcrypt.compare(currentPassword, user.password))) {
    const err = new Error(`የተሳሳተ የይለፍ ቃል!`);
    err.statusCode = 400;
    throw err;
   
  }

  const hashedPassword = await bcrypt.hash(newPassword, 8);
  await userModel.resetPassword(userId, hashedPassword);

}

const changeUserPassword = async (userId,newPassword) => {
 const Id = Number(userId);
  const user = await userModel.findUserById(Id);
  if(!user) {
    const err = new Error(`something went wrong!`);
    err.statusCode = 400;
    throw err;
  }
  const hashedPassword = await bcrypt.hash(newPassword, 8);
  await userModel.resetPassword(Id, hashedPassword);

}






module.exports = {
  register,
  login,
  getUsers,
  updateUser,
  activeUser,
  deActiveUser,
  getUserById,
  changePassword,
  changeUserPassword 
 
};
