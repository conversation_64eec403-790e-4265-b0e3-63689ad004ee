// services/dashboard.service.js
const { pool, ensureConnection } = require('../config/db');
const logger = require('../logger');

/**
 * Get total number of available devices
 * @returns {Promise<Object>} Object containing the count of all devices
 */
const getTotalDevices = async () => {
  try {
    // Ensure database connection is active before executing queries
    const activePool = await ensureConnection();
    
    const result = await activePool.request().query(`
      SELECT COUNT(*) AS totalDevices
      FROM tbl_devices
    `);
    
    return {
      totalDevices: result.recordset[0].totalDevices
    };
  } catch (err) {
    logger.error('Error getting total devices count:', err);
    throw err;
  }
};

/**
 * Get count of active and offline devices
 * @returns {Promise<Object>} Object containing counts of active and offline devices
 */
const getDeviceStatusCounts = async () => {
  try {
    // Ensure database connection is active before executing queries
    const activePool = await ensureConnection();
    
    const result = await activePool.request().query(`
      SELECT 
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS activeDevices,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS offlineDevices,
        COUNT(*) AS totalDevices
      FROM tbl_devices
    `);
    
    return {
      activeDevices: result.recordset[0].activeDevices || 0,
      offlineDevices: result.recordset[0].offlineDevices || 0,
      totalDevices: result.recordset[0].totalDevices || 0
    };
  } catch (err) {
    logger.error('Error getting device status counts:', err);
    throw err;
  }
};

/**
 * Get total number of online and offline content across all devices
 * @returns {Promise<Object>} Object containing counts of online and offline content
 */
const getContentStatusCounts = async () => {
  try {
    // Ensure database connection is active before executing queries
    const activePool = await ensureConnection();
    
    const result = await activePool.request().query(`
      SELECT 
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS onlineContent,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS offlineContent,
        COUNT(*) AS totalContent
      FROM tbl_screen_post
    `);
    
    return {
      onlineContent: result.recordset[0].onlineContent || 0,
      offlineContent: result.recordset[0].offlineContent || 0,
      totalContent: result.recordset[0].totalContent || 0
    };
  } catch (err) {
    logger.error('Error getting content status counts:', err);
    throw err;
  }
};

/**
 * Get count of online content per device
 * @returns {Promise<Array>} Array of devices with their online content count
 */
const getContentCountPerDevice = async () => {
  try {
    // Ensure database connection is active before executing queries
    const activePool = await ensureConnection();
    
    const result = await activePool.request().query(`
      SELECT 
        d.id,
        d.name,
        d.deviceId,
        d.status AS deviceStatus,
        COUNT(sp.id) AS totalContent,
        SUM(CASE WHEN sp.status = 1 THEN 1 ELSE 0 END) AS onlineContent
      FROM tbl_devices d
      LEFT JOIN tbl_screen_post sp ON d.id = sp.deviceId
      GROUP BY d.id, d.name, d.deviceId, d.status
      ORDER BY d.name
    `);
    
    return result.recordset;
  } catch (err) {
    logger.error('Error getting content count per device:', err);
    throw err;
  }
};

/**
 * Get count of video and image content per device
 * @returns {Promise<Array>} Array of devices with their video and image content counts
 */
const getContentTypeCountPerDevice = async () => {
  try {
    // Ensure database connection is active before executing queries
    const activePool = await ensureConnection();
    
    const result = await activePool.request().query(`
      SELECT 
        d.id,
        d.name,
        d.deviceId,
        d.status AS deviceStatus,
        SUM(CASE WHEN sp.fileType = 'video' AND sp.status = 1 THEN 1 ELSE 0 END) AS videoContent,
        SUM(CASE WHEN sp.fileType = 'image' AND sp.status = 1 THEN 1 ELSE 0 END) AS imageContent,
        SUM(CASE WHEN sp.status = 1 THEN 1 ELSE 0 END) AS totalOnlineContent
      FROM tbl_devices d
      LEFT JOIN tbl_screen_post sp ON d.id = sp.deviceId
      GROUP BY d.id, d.name, d.deviceId, d.status
      ORDER BY d.name
    `);
    
    return result.recordset;
  } catch (err) {
    logger.error('Error getting content type count per device:', err);
    throw err;
  }
};

module.exports = {
  getTotalDevices,
  getDeviceStatusCounts,
  getContentStatusCounts,
  getContentCountPerDevice,
  getContentTypeCountPerDevice
};
