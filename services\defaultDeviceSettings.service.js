const { pool, ensureConnection } = require('../config/db');
const logger = require('../logger');

/**
 * Get all default device settings
 * @returns {Promise<Array>} Array of all settings
 */
const getAllSettings = async () => {
  try {
    const activePool = await ensureConnection();
    const result = await activePool.request().query(`
      SELECT
        s.*,
        d.name AS deviceName,
        d.deviceId AS deviceIdentifier,
        d.status AS deviceStatus
      FROM tbl_default_device_settings s
      LEFT JOIN tbl_devices d ON s.deviceId = d.id
      ORDER BY s.createdAt DESC
    `);

    // Transform the result into nested structure
    const formatted = result.recordset.map(row => {
      const {
        deviceName,
        deviceIdentifier,
        deviceStatus,
        ...setting
      } = row;

      return {
        ...setting,
        device: {
          name: deviceName,
          deviceId: deviceIdentifier,
          status: deviceStatus
        }
      };
    });

    return formatted;
  } catch (err) {
    logger.error('Error getting all default device settings:', err);
    throw err;
  }
};

/**
 * Get a setting by ID
 * @param {number} id - The setting ID
 * @returns {Promise<Object>} The setting object
 */
const getSettingById = async (id) => {
  try {
    const activePool = await ensureConnection();
    const result = await activePool.request()
      .input('id', id)
      .query(`
        SELECT
          s.*,
          d.name AS deviceName,
          d.deviceId AS deviceIdentifier,
          d.status AS deviceStatus
        FROM tbl_default_device_settings s
        LEFT JOIN tbl_devices d ON s.deviceId = d.id
        WHERE s.id = @id
      `);

    if (result.recordset.length === 0) {
      return null;
    }

    const row = result.recordset[0];
    const {
      deviceName,
      deviceIdentifier,
      deviceStatus,
      ...setting
    } = row;

    return {
      ...setting,
      device: {
        name: deviceName,
        deviceId: deviceIdentifier,
        status: deviceStatus
      }
    };
  } catch (err) {
    logger.error(`Error getting setting by ID ${id}:`, err);
    throw err;
  }
};

/**
 * Get settings by device ID
 * @param {number} deviceId - The device ID
 * @returns {Promise<Array>} Array of settings for the device
 */
const getSettingsByDeviceId = async (deviceId) => {
  try {
    const activePool = await ensureConnection();
    const result = await activePool.request()
      .input('deviceId', deviceId)
      .query(`
        SELECT * FROM tbl_default_device_settings
        WHERE deviceId = @deviceId
        ORDER BY createdAt DESC
      `);

    return result.recordset;
  } catch (err) {
    logger.error(`Error getting settings by device ID ${deviceId}:`, err);
    throw err;
  }
};

/**
 * Get settings by user ID
 * @param {number} userId - The user ID
 * @returns {Promise<Array>} Array of settings for the user
 */
const getSettingsByUserId = async (userId) => {
  try {
    const activePool = await ensureConnection();
    const result = await activePool.request()
      .input('userId', userId)
      .query(`
        SELECT
          s.*,
          d.name AS deviceName,
          d.deviceId AS deviceIdentifier,
          d.status AS deviceStatus
        FROM tbl_default_device_settings s
        LEFT JOIN tbl_devices d ON s.deviceId = d.id
        WHERE s.userId = @userId
        ORDER BY s.createdAt DESC
      `);

    // Transform the result into nested structure
    const formatted = result.recordset.map(row => {
      const {
        deviceName,
        deviceIdentifier,
        deviceStatus,
        ...setting
      } = row;

      return {
        ...setting,
        device: {
          name: deviceName,
          deviceId: deviceIdentifier,
          status: deviceStatus
        }
      };
    });

    return formatted;
  } catch (err) {
    logger.error(`Error getting settings by user ID ${userId}:`, err);
    throw err;
  }
};

/**
 * Create a new device setting
 * @param {Object} data - The setting data
 * @returns {Promise<Object>} The result of the operation
 */
const createSetting = async (data) => {
  try {
    const activePool = await ensureConnection();

    // Build the query dynamically based on provided fields
    let fields = ['userId', 'deviceId'];
    let values = ['@userId', '@deviceId'];

    // Create the request with required parameters
    const request = activePool.request()
      .input('userId', data.userId)
      .input('deviceId', data.device);

    // Add optional filePath if provided
    if (data.filePath) {
      fields.push('filePath');
      values.push('@filePath');
      request.input('filePath', data.filePath);
    }

    // Add default_Id if provided
    if (data.default_Id) {
      fields.push('default_Id');
      values.push('@default_Id');
      request.input('default_Id', data.default_Id);
    }

    // Add timestamp fields
    fields.push('createdAt', 'updatedAt');
    values.push('GETDATE()', 'GETDATE()');

    const query = `
      INSERT INTO tbl_default_device_settings (${fields.join(', ')})
      VALUES (${values.join(', ')});

      SELECT SCOPE_IDENTITY() AS id;
    `;

    const result = await request.query(query);
    return { id: result.recordset[0].id };
  } catch (err) {
    logger.error('Error creating device setting:', err);
    throw err;
  }
};

/**
 * Update a device setting
 * @param {number} id - The setting ID
 * @param {Object} data - The updated setting data
 * @returns {Promise<Object>} The result of the operation
 */
const updateSetting = async (id, data) => {
  try {
    const activePool = await ensureConnection();
    const request = activePool.request().input('id', id);

    // Build SET clauses dynamically
    const setClauses = [];

    if (data.userId !== undefined) {
      request.input('userId', data.userId);
      setClauses.push('userId = @userId');
    }



    if (data.default_Id !== undefined) {
      request.input('default_Id', data.default_Id);
      setClauses.push('default_Id = @default_Id');
    }

    if (data.filePath !== undefined) {
      request.input('filePath', data.filePath);
      setClauses.push('filePath = @filePath');
    }

    // Always update the updatedAt timestamp
    setClauses.push('updatedAt = GETDATE()');

    if (setClauses.length === 1) {
      // Only updatedAt is being updated, nothing else changed
      return { rowsAffected: 0 };
    }

    const query = `
      UPDATE tbl_default_device_settings SET
        ${setClauses.join(',\n')}
      WHERE id = @id
    `;

    const result = await request.query(query);
    return result;
  } catch (err) {
    logger.error(`Error updating setting with ID ${id}:`, err);
    throw err;
  }
};

/**
 * Delete a device setting
 * @param {number} id - The setting ID
 * @returns {Promise<Object>} The result of the operation
 */
const deleteSetting = async (id) => {
  try {
    const activePool = await ensureConnection();
    const result = await activePool.request()
      .input('id', id)
      .query('DELETE FROM tbl_default_device_settings WHERE id = @id');

    return result;
  } catch (err) {
    logger.error(`Error deleting setting with ID ${id}:`, err);
    throw err;
  }
};

/**
 * Get default settings by device ID string
 * This function finds the device by its deviceId string, then gets all settings for that device
 * and joins with tbl_default to get the default settings details
 * @param {string} deviceIdString - The device ID string from the frontend
 * @returns {Promise<Object>} - Device settings with default information
 */
const getSettingsByDeviceIdString = async (deviceIdString) => {
  try {
    // Ensure database connection is active before executing queries
    const activePool = await ensureConnection();

    // First, find the device by its deviceId string
    const deviceResult = await activePool.request()
      .input('deviceId', deviceIdString)
      .query('SELECT id FROM tbl_devices WHERE deviceId = @deviceId');

    // If no device found, return empty object
    if (deviceResult.recordset.length === 0) {
      return {};
    }

    // Get the device ID (numeric)
    const deviceId = deviceResult.recordset[0].id;

    // Get settings for this device with joined default information
    const result = await activePool.request()
      .input('deviceId', deviceId)
      .query(`
        SELECT
          dds.id, dds.userId, dds.deviceId, dds.default_Id, dds.filePath, dds.createdAt, dds.updatedAt,
          d.name AS deviceName, d.deviceId AS deviceIdentifier, d.status AS deviceStatus,
          def.id AS defaultId, def.name AS defaultName, def.type AS defaultType
        FROM tbl_default_device_settings dds
        LEFT JOIN tbl_devices d ON dds.deviceId = d.id
        LEFT JOIN tbl_default def ON dds.default_Id = def.id
        WHERE dds.deviceId = @deviceId
        ORDER BY dds.createdAt DESC
      `);

    if (result.recordset.length === 0) {
      return {};
    }

    // Transform the first result into a structured object
    const row = result.recordset[0];
    const {
      deviceName,
      deviceIdentifier,
      deviceStatus,
      defaultId,
      defaultName,
      defaultType,
      ...setting
    } = row;

    return {
      ...setting,
      device: {
        name: deviceName,
        deviceId: deviceIdentifier,
        status: deviceStatus
      },
      default: defaultId ? {
        id: defaultId,
        name: defaultName,
        type: defaultType
      } : null
    };
  } catch (err) {
    logger.error('Error getting settings by device ID string:', err);
    throw err;
  }
};

const getDefaultSetting = async () => {
  try {
    const query = `SELECT * FROM tbl_default WHERE type='animation'`;
      const activePool = await ensureConnection();
    const result = await activePool.request().query(query);
    return result.recordset[0];
  }catch (err) {
   logger.error('Error getting settings by device ID string:', err);
  }
}

module.exports = {
  getAllSettings,
  getSettingById,
  getSettingsByDeviceId,
  getSettingsByUserId,
  createSetting,
  updateSetting,
  deleteSetting,
  getSettingsByDeviceIdString,
  getDefaultSetting
};
