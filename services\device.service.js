const { pool } = require("../config/db");

const getAllDevices = async () => {
  const result = await pool
    .request()
    .query("SELECT * FROM tbl_devices ORDER BY createdAt DESC");
  return result.recordset;
};

const getDeviceById = async (id) => {
  const result = await pool
    .request()
    .input("id", id)
    .query("SELECT * FROM tbl_devices WHERE id = @id");
  return result.recordset[0];
};

const createDevice = async (data) => {
  // Get the highest screen number from names like 'screen 1', 'screen 2'
  const result = await pool.request().query(`
      SELECT TOP 1 name FROM tbl_devices
      WHERE name LIKE 'screen %'
      ORDER BY 
        TRY_CAST(REPLACE(name, 'screen ', '') AS INT) DESC
    `);

  let nextNumber = 1;

  if (result.recordset.length > 0) {
    const lastName = result.recordset[0].name;
    const lastNumber = parseInt(lastName.replace("screen ", ""));
    if (!isNaN(lastNumber)) {
      nextNumber = lastNumber + 1;
    }
  }

  const generatedName = `screen ${nextNumber}`;

  const insert = await pool
  .request()
  .input("name", generatedName)
  .input("deviceId", data.deviceId)
  .input("status", data.status || 0)
  .query(`
    INSERT INTO tbl_devices (name, deviceId, status, createdAt, updatedAt)
    OUTPUT INSERTED.id
    VALUES (@name, @deviceId, @status, GETDATE(), GETDATE())
  `);


  return insert.recordset[0].id;
};

const updateDevice = async (id, data) => {
  const result = await pool
    .request()
    .input("id", id)
    .input("deviceId", data.deviceId)
    .input("status", data.status).query(`
      UPDATE tbl_devices SET
        deviceId = @deviceId,
        status = @status,
        updatedAt = GETDATE()
      WHERE id = @id
    `);
  return result;
};

const deleteDevice = async (id) => {
  const result = await pool
    .request()
    .input("id", id)
    .query("DELETE FROM tbl_devices WHERE id = @id");
  return result;
};

const findDeviceByDeviceId = async (deviceId) => {
  const result = await pool
    .request()
    .input("deviceId", deviceId)
    .query("SELECT * FROM tbl_devices WHERE deviceId = @deviceId");
  return result.recordset[0];
};

const deviceSettings = async (data) => {
  const device = await findDeviceByDeviceId(data.deviceId);
  if (device) {
    const result = await pool
      .request()
      .input("deviceId", data.deviceId)
      .input("default_settings", JSON.stringify(data.default_settings)).query(`
    UPDATE tbl_default_device_settings SET
      default_settings = @default_settings,
      updatedAt = GETDATE()
      WHERE deviceId = @deviceId
  `);
    return result;
  } else {
    const insert = await pool
      .request()
      .input("deviceId", data.deviceId)
      .input("default_settings", data.default_settings).query(`
    INSERT INTO tbl_default_device_settings 
    (deviceId, default_settings, createdAt, updatedAt)
    VALUES (@deviceId, @default_settings, GETDATE(), GETDATE())
  `);

    return insert;
  }
};


const findDefault = async () => {
  const result = await pool
    .request()
    .query("SELECT * FROM tbl_default");
    console.log(result.recordset);
  return result.recordset;
};

module.exports = {
  getAllDevices,
  getDeviceById,
  createDevice,
  updateDevice,
  deleteDevice,
  findDeviceByDeviceId,
  deviceSettings,
  findDefault
};
