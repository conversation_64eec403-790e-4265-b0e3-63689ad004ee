const { ensureConnection } = require("../config/db");
const sql = require("mssql");
const ethiopianDate = require("./converter");

class EventsService {
  async createEvent(eventData, userId) {
    const pool = await ensureConnection();
    const request = pool.request();
    const { title, desciption, start_date, end_date, status } = eventData;

    const result = await request
      .input("title", sql.NVarChar, title)
      .input("desciption", sql.NVarChar, desciption)
      .input("start_date", sql.DateTime, start_date ? new Date(start_date) : null)
      .input("end_date", sql.DateTime, end_date ? new Date(end_date) : null)
      .input("status", sql.Int, status || 1)
      .input("postedby", sql.Int, userId)
      .query(
        "INSERT INTO tbl_events (title, desciption, start_date, end_date, status, createdAt, updatedAt, postedby) OUTPUT INSERTED.* VALUES (@title, @desciption, @start_date, @end_date, @status, GETDATE(), GETDATE(), @postedby)"
      );
    return result.recordset[0];
  }

  async toEthiopian(year, mm, dd) {
    const date = [year, mm, dd];
    const gregorian = ethiopianDate.toEthiopian(date);
    return gregorian;
  }

  async getEvents(page = 1, limit = 10) {
    const pool = await ensureConnection();
    const offset = (page - 1) * limit;

    const countResult = await pool
      .request()
      .query("SELECT COUNT(*) as count FROM tbl_events");
    const totalItems = countResult.recordset[0].count;
    const totalPages = Math.ceil(totalItems / limit);

    const result = await pool
      .request()
      .input("offset", sql.Int, offset)
      .input("limit", sql.Int, limit)
      .query(
        "SELECT * FROM tbl_events ORDER BY id DESC OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY"
      );

    const events = await Promise.all(
      result.recordset.map(async (event) => {
        let startDateEthiopian = null;
        let endDateEthiopian = null;

        if (event.start_date) {
          const startDate = new Date(event.start_date);
          startDateEthiopian = await this.toEthiopian(
            startDate.getFullYear(),
            startDate.getMonth() + 1, // JS months are 0-based
            startDate.getDate()
          );
        }

        if (event.end_date) {
          const endDate = new Date(event.end_date);
          endDateEthiopian = await this.toEthiopian(
            endDate.getFullYear(),
            endDate.getMonth() + 1, // JS months are 0-based
            endDate.getDate()
          );
        }

        return {
          ...event,
          startDateEthiopian,
          endDateEthiopian,
        };
      })
    );

    return {
      data: events,
      pagination: {
        totalItems,
        totalPages,
        currentPage: page,
        pageSize: limit,
      },
    };
  }

  async getEventById(id) {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .input("id", sql.Int, id)
      .query("SELECT * FROM tbl_events WHERE id = @id");
    return result.recordset[0];
  }

  async getActiveEvents() {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .query(
        "SELECT * FROM tbl_events WHERE status = 1 AND (end_date IS NULL OR end_date > GETDATE()) ORDER BY id DESC"
      );
    return result.recordset;
  }

  async updateEvent(id, eventData) {
    const pool = await ensureConnection();
    const { title, desciption, start_date, end_date, status } = eventData;
    const request = pool.request().input("id", sql.Int, id);

    const setClauses = [];

    if (title !== undefined) {
      setClauses.push("title = @title");
      request.input("title", sql.NVarChar, title);
    }
    if (desciption !== undefined) {
      setClauses.push("desciption = @desciption");
      request.input("desciption", sql.NVarChar, desciption);
    }
    if (start_date !== undefined) {
      setClauses.push("start_date = @start_date");
      request.input("start_date", sql.DateTime, start_date ? new Date(start_date) : null);
    }
    if (end_date !== undefined) {
      setClauses.push("end_date = @end_date");
      request.input("end_date", sql.DateTime, end_date ? new Date(end_date) : null);
    }
    if (status !== undefined) {
      setClauses.push("status = @status");
      request.input("status", sql.Int, status);
    }

    if (setClauses.length === 0) {
      return true;
    }

    let query = `UPDATE tbl_events SET ${setClauses.join(
      ", "
    )}, updatedAt = GETDATE() WHERE id = @id`;

    const result = await request.query(query);
    return result.rowsAffected[0] > 0;
  }

  async deleteEvent(id) {
    const pool = await ensureConnection();
    const result = await pool
      .request()
      .input("id", sql.Int, id)
      .query("DELETE FROM tbl_events WHERE id = @id");
    return result.rowsAffected[0] > 0;
  }
}

module.exports = new EventsService();
