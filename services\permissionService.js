const permissionModel = require("../models/permissionModel");

/**
 * Get all permissions
 * @returns {Promise<Array>} Array of all permissions
 */
const getAllPermissions = async () => {
  return await permissionModel.getAllPermissions();
};

/**
 * Get permission by ID
 * @param {number} id - Permission ID
 * @returns {Promise<Object>} Permission object
 */
const getPermissionById = async (id) => {
  const permission = await permissionModel.getPermissionById(id);
  if (!permission) {
    const err = new Error(`Permission with ID ${id} not found`);
    err.statusCode = 404;
    throw err;
  }
  return permission;
};

/**
 * Get permissions by user ID
 * @param {number} userId - User ID
 * @returns {Promise<Array>} Array of permissions for the user
 */
const getPermissionsByUserId = async (userId) => {
  return await permissionModel.getPermissionsByUserId(userId);
};

/**
 * Get permissions by menu ID
 * @param {string} navMenuId - Navigation menu ID
 * @returns {Promise<Array>} Array of permissions for the menu
 */
const getPermissionsByMenuId = async (navMenuId) => {
  return await permissionModel.getPermissionsByMenuId(navMenuId);
};

/**
 * Create a new permission
 * @param {Object} permissionData - Permission data object
 * @returns {Promise<Object>} Created permission
 */
const createPermission = async (permissionData) => {
  // Validate required fields
  if (!permissionData.userId || !permissionData.nav_menuId) {
    const err = new Error("User ID and Menu ID are required");
    err.statusCode = 400;
    throw err;
  }

  // Check if permission already exists
  const existingPermissions = await permissionModel.getPermissionsByUserId(
    permissionData.userId
  );
  const permissionExists = existingPermissions.some(
    (p) => p.nav_menuId === permissionData.nav_menuId
  );

  if (permissionExists) {
    const err = new Error("Permission already exists for this user and menu");
    err.statusCode = 400;
    throw err;
  }

  return await permissionModel.createPermission(permissionData);
};

/**
 * Update an existing permission
 * @param {number} id - Permission ID
 * @param {Object} permissionData - Permission data object
 * @returns {Promise<Object>} Updated permission
 */
const updatePermission = async (id, permissionData) => {
  // Check if permission exists
  const permission = await permissionModel.getPermissionById(id);
  if (!permission) {
    const err = new Error(`Permission with ID ${id} not found`);
    err.statusCode = 404;
    throw err;
  }

  // Validate required fields
  if (!permissionData.userId || !permissionData.nav_menuId) {
    const err = new Error("User ID and Menu ID are required");
    err.statusCode = 400;
    throw err;
  }

  await permissionModel.updatePermission(id, permissionData);
  return await permissionModel.getPermissionById(id);
};

/**
 * Delete a permission by ID
 * @param {number} id - Permission ID
 * @returns {Promise<void>}
 */
const deletePermission = async (id) => {
  // Check if permission exists
  const permission = await permissionModel.getPermissionById(id);
  if (!permission) {
    const err = new Error(`Permission with ID ${id} not found`);
    err.statusCode = 404;
    throw err;
  }

  await permissionModel.deletePermission(id);
};

/**
 * Delete permissions by user ID and menu ID
 * @param {number} userId - User ID
 * @param {string} menuId - Menu ID
 * @returns {Promise<void>}
 */
const deletePermissionByUserAndMenu = async (userId, menuId) => {
  await permissionModel.deletePermissionByUserAndMenu(userId, menuId);
};

/**
 * Create multiple permissions for a user
 * @param {Object} userData - Object containing userId and array of menu IDs
 * @returns {Promise<void>}
 */
const createMultiplePermissions = async (userData) => {
  if (!userData.userId || !userData.Menu || !Array.isArray(userData.Menu)) {
    const err = new Error("User ID and Menu array are required");
    err.statusCode = 400;
    throw err;
  }
  const isValid = Array.isArray(userData.Menu) &&
  userData.Menu.length > 0 &&
  userData.Menu.every(item => typeof item === 'string');
  if (isValid) {
    const permissions = permissionModel.getPermissionsByUserId(userData.userId);
    if(permissions.length > 0){
      await permissionModel.deletePermissionByUserId(userData.userId);
    }
    for (let menu of userData.Menu) {
      const data = {
        nav_menuId: menu,
        userId: userData.userId,
      };
      await permissionModel.createPermission(data);
    }
  }
};

module.exports = {
  getAllPermissions,
  getPermissionById,
  getPermissionsByUserId,
  getPermissionsByMenuId,
  createPermission,
  updatePermission,
  deletePermission,
  deletePermissionByUserAndMenu,
  createMultiplePermissions,
};
