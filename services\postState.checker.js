// services/postState.checker.js

const screenPostService = require('./screenPost.service');
const socketManager = require('../socket'); // Adjust path to your main socket.js file
const logger = require('../logger'); // Assuming you have a logger

let pollingInterval;
let previousState = null; // Variable to store the last known state of the posts

/**
 * Checks the current state of active posts against the previous state.
 * If there's a change, it broadcasts the update to all office apps.
 */
const checkAndBroadcastUpdates = async () => {
  try {
    // 1. Fetch the current list of all active posts from the database.
    const currentPosts = await screenPostService.getAllActivePosts();

    // 2. Convert the post lists to a simple, comparable string.
    // An array of IDs is a good, lightweight way to check for changes.
    const currentStateString = currentPosts.map(p => p.id).sort().join(',');

    // 3. Compare with the previous state.
    // If it's the first run, `previousState` will be null.
    if (previousState !== null && currentStateString !== previousState) {
      logger.info('Post state has changed. Broadcasting updates to office apps...');
      // If the state is different, broadcast the full, new list of posts.
      await socketManager.broadcastToOfficeApps(currentPosts); // Pass the already fetched posts
    } else if (previousState === null) {
      logger.info('Post state checker initialized. Storing initial state.');
    }
    
    // 4. Update the previous state for the next check.
    previousState = currentStateString;

  } catch (error) {
    logger.error('Error in post state checker:', error);
  }
};

/**
 * Starts the polling loop.
 * @param {number} interval - The interval in milliseconds to check for updates.
 */
const start = (interval = 1000) => { // Default to 10 seconds
  if (pollingInterval) {
    logger.warn('Post state checker is already running.');
    return;
  }
  
  logger.info(`Starting post state checker with a ${interval / 1000}-second interval.`);
  
  // Run the check immediately on start, then set the interval.
  checkAndBroadcastUpdates();
  pollingInterval = setInterval(checkAndBroadcastUpdates, interval);
};

/**
 * Stops the polling loop.
 */
const stop = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
    previousState = null; // Reset state
    logger.info('Post state checker stopped.');
  }
};

module.exports = {
  start,
  stop,
};