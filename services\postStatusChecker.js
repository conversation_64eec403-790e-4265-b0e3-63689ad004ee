// services/postStatusChecker.js
const PostService = require('./screenPost.service');
const logger = require('../logger');

let isRunning = false;
let checkTimeoutId = null;
const CHECK_INTERVAL_MS = 1000; // Check every 1000ms (1 second)

/**
 * The core function that calls the PostService to check and update post statuses.
 * This runs in a continuous loop via setTimeout.
 */
async function runCheckCycle() {
    if (!isRunning) {
        logger.info('Post status checker loop stopping.');
        return; // Exit the loop if stop() has been called
    }

    // logger.debug('Post status checker: Running check cycle...');

    try {
        // Call the PostService method that contains your SQL logic
        await PostService.updateScheduledPosts();

    } catch (error) {
        // Log errors but the loop continues unless 'isRunning' is set to false
        logger.error('Post status checker: Error during check cycle:', error);

        // Handle database connection errors specifically
        if (error.message && error.message.includes('Connection is closed')) {
            logger.warn('Post status checker: Database connection was closed. Will retry on next cycle.');

            // For connection errors, we might want to wait a bit longer before the next attempt
            if (isRunning) {
                checkTimeoutId = setTimeout(runCheckCycle, CHECK_INTERVAL_MS * 5); // Wait 5 times longer (5 seconds)
                logger.info(`Post status checker: Scheduled next check in ${CHECK_INTERVAL_MS * 5}ms due to connection error.`);
                return; // Exit early with the longer timeout already set
            }
        }
    }

    // Schedule the next check *after* the current one finishes (using 'finally' is also an option)
    if (isRunning) {
        checkTimeoutId = setTimeout(runCheckCycle, CHECK_INTERVAL_MS);
    } else {
        logger.info('Post status checker loop exiting after current cycle.');
    }
}

/**
 * Starts the continuous post status checker.
 */
function start() {
    if (isRunning) {
        logger.warn('Post status checker is already running.');
        return;
    }
    isRunning = true;
    logger.info('Post status checker starting...');
    // Start the first check cycle
    // Use setImmediate or setTimeout(..., 0) to run after current event loop turn
    setImmediate(runCheckCycle);
}

/**
 * Stops the continuous post status checker.
 */
function stop() {
    if (!isRunning) {
        logger.warn('Post status checker is not running.');
        return;
    }
    isRunning = false;
    // Clear the *next* scheduled timeout. The current run will finish.
    if (checkTimeoutId) {
        clearTimeout(checkTimeoutId);
        checkTimeoutId = null;
    }
    logger.info('Post status checker requested to stop. Waiting for current cycle to finish...');
    // The loop will naturally exit after the current run because isRunning is false.
}

module.exports = {
    start,
    stop
};