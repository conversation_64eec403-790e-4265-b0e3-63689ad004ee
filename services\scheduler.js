// services/scheduler.js
const schedule = require('node-schedule');
const logger = require('../logger'); // Assuming you have a logger utility

const scheduledJobs = {}; // Keep track of scheduled jobs

/**
 * Schedules a task to run based on a cron pattern or date.
 * @param {string} jobName - A unique name for the job.
 * @param {string | Date | schedule.RecurrenceRule} rule - Cron pattern, Date object, or RecurrenceRule.
 * @param {Function} task - The function to execute.
 */
function scheduleTask(jobName, rule, task) {
    if (scheduledJobs[jobName]) {
        logger.warn(`Job "${jobName}" already scheduled. Skipping.`);
        return scheduledJobs[jobName];
    }

    logger.info(`Scheduling job "${jobName}" with rule: ${rule}`);

    const job = schedule.scheduleJob(rule, async () => {
        logger.info(`Running job "${jobName}"...`);
        try {
            await task(); // Execute the task (handle async tasks)
            logger.info(`Job "${jobName}" finished successfully.`);
        } catch (error) {
            logger.error(`Job "${jobName}" failed:`, error);
            // Add more sophisticated error handling/reporting if needed
        }
    });

    if (job) {
        scheduledJobs[jobName] = job;
        logger.info(`Job "${jobName}" scheduled successfully.`);
    } else {
        logger.error(`Failed to schedule job "${jobName}". Check the rule format.`);
    }

    return job;
}

/**
 * Cancels a previously scheduled task.
 * @param {string} jobName - The unique name of the job to cancel.
 * @returns {boolean} - True if cancelled, false otherwise.
 */
function cancelTask(jobName) {
    const job = scheduledJobs[jobName];
    if (job) {
        const cancelled = job.cancel();
        if (cancelled) {
            delete scheduledJobs[jobName];
            logger.info(`Job "${jobName}" cancelled successfully.`);
            return true;
        } else {
            logger.warn(`Could not cancel job "${jobName}" (might be running).`);
            return false;
        }
    } else {
        logger.warn(`Job "${jobName}" not found for cancellation.`);
        return false;
    }
}

/**
 * Get details of a scheduled job.
 * @param {string} jobName - The unique name of the job.
 * @returns {schedule.Job | undefined}
 */
function getJob(jobName) {
   return scheduledJobs[jobName];
}

// Function to initialize all predefined jobs when the app starts
function initializeScheduledJobs() {
    logger.info('Initializing scheduled jobs...');

    // Example Job 1: Run every minute
    scheduleTask('log-every-minute', '* * * * *', () => {
        logger.debug('This task runs every minute!');
        // Add your task logic here, e.g., call another service
    });

    // Example Job 2: Run at 2:30 AM every day
    scheduleTask('daily-report', '30 2 * * *', async () => {
        logger.info('Generating daily report...');
        // Simulate async work
        await new Promise(resolve => setTimeout(resolve, 5000));
        logger.info('Daily report generated.');
    });

     // Example Job 3: Run once at a specific date/time
     const runOnceDate = new Date(Date.now() + 10000); // Run 10 seconds from now
     scheduleTask('run-once-task', runOnceDate, () => {
         logger.info('This task runs only once at a specific time.');
     });

    // Add more jobs here... maybe load them from a configuration file or database
}

module.exports = {
    scheduleTask,
    cancelTask,
    getJob,
    initializeScheduledJobs
};