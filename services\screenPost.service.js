const { pool, ensureConnection } = require('../config/db');
const settingsService = require('../services/settingsService');
const logger = require('../logger');

const getAllScreenPosts = async () => {
  const result = await pool.request().query(`
    SELECT TOP (1000)
      sp.*,
      d.name AS deviceName,
      d.status AS deviceStatus,
      d.createdAt AS deviceCreatedAt,
      d.updatedAt AS deviceUpdatedAt
    FROM tbl_screen_post sp
    LEFT JOIN tbl_devices d ON sp.deviceId = d.id
    ORDER BY sp.createdAt DESC
  `);

  // Transform the result into nested structure
  const formatted = result.recordset.map(row => {
    const {
      deviceName,
      deviceStatus,
      deviceCreatedAt,
      deviceUpdatedAt,
      ...post
    } = row;

    return {
      ...post,
      device: {
        name: deviceName,
        status: deviceStatus,
        createdAt: deviceCreatedAt,
        updatedAt: deviceUpdatedAt
      }
    };
  });

  return formatted;
};

const getAllActivePosts = async () => {
  try {
    // Ensure the database connection is active
    const activePool = await ensureConnection();

    // Query for all posts where the status is '1' (active)
    const result = await activePool.request().query(`
      SELECT
        id,
        filePath,
        fileType,
        priority,
        postedAt
      FROM tbl_screen_post
      WHERE status = 1
      ORDER BY priority DESC, postedAt DESC
    `);
   
    // The result is an array of active posts, ready to be sent.
    return result.recordset;

  } catch (err) {
    logger.error('Error in getAllActivePosts:', err);
    // Re-throw the error so the caller (socket.js) can handle it
    throw err;
  }
};


const getScreenPostsByUserId = async (userId) => {
  const result = await pool.request()
    .input('userId', userId)
    .query(`
      SELECT TOP (1000)
        sp.*,
        d.name AS deviceName,
        d.status AS deviceStatus,
        d.createdAt AS deviceCreatedAt,
        d.updatedAt AS deviceUpdatedAt
      FROM tbl_screen_post sp
      LEFT JOIN tbl_devices d ON sp.deviceId = d.id
      WHERE sp.userId = @userId
      ORDER BY sp.createdAt DESC
    `);

  const formatted = await Promise.all(result.recordset.map(async row => {
    const {
      deviceName,
      deviceStatus,
      deviceCreatedAt,
      deviceUpdatedAt,
      startDate,
      endDate,
      ...post
    } = row;

    // Convert to Ethiopian if not null
    const convertToEthiopian = async (date) => {
      if (!date) return null;
      const jsDate = new Date(date);

      const [ethYear, ethMonth, ethDay] = await settingsService.toEthiopian(jsDate.getFullYear(), jsDate.getMonth() + 1, jsDate.getDate());
      return `${ethYear}-${ethMonth.toString().padStart(2, '0')}-${ethDay.toString().padStart(2, '0')}`;
    };

    return {
      ...post,
      startDate: await convertToEthiopian(startDate),
      endDate: await convertToEthiopian(endDate),
      device: {
        name: deviceName,
        status: deviceStatus,
        createdAt: deviceCreatedAt,
        updatedAt: deviceUpdatedAt
      }
    };
  }));


  return formatted;
};





const getScreenPostById = async (id) => {
  const result = await pool.request()
    .input('id', id)
    .query('SELECT * FROM tbl_screen_post WHERE id = @id');
  return result.recordset[0];
};


const createScreenPost = async (data) => {
  try{
   let startDate = null; // Will store the final Date object representing UTC time
    let endDate = null;   // Will store the final Date object representing UTC time

    // Check if scheduled and if the necessary fields are provided by the frontend
    // Now expecting year/month/day models and startTime/endTime strings
    if (Number(data.isScheduled) === 1) {
        // Validate required inputs for scheduling
        // Check for presence of Ethiopian date components and time strings
        if (data.yearStartDateModel == null || data.monthStartDateModel == null || data.dayStartDateModel == null ||
            data.yearEndDateModel == null || data.monthEndDateModel == null || data.dayEndDateModel == null
            ) {
             console.error("Missing date or time data for scheduled posting.");
             const err = new Error("Scheduled posting requires start/end date and time.");
             err.statusCode = 400;
             throw err;
        }

        // Extract Ethiopian date components (ensure they are numbers)
        // The frontend sends these explicitly now
        const ethYearStart = Number(data.yearStartDateModel);
        const ethMonthStart = Number(data.monthStartDateModel); // Assuming 1-based from frontend
        const ethDayStart = Number(data.dayStartDateModel);

        const ethYearEnd = Number(data.yearEndDateModel);
        const ethMonthEnd = Number(data.monthEndDateModel);   // Assuming 1-based from frontend
        const ethDayEnd = Number(data.dayEndDateModel);


        const gregorianStartDateArray = await settingsService.toGregorian(ethYearStart, ethMonthStart, ethDayStart);
        const gregorianEndDateArray = await settingsService.toGregorian(ethYearEnd, ethMonthEnd, ethDayEnd);

        // --- Validate the output of settingsService.toGregorian ---
        if (!Array.isArray(gregorianStartDateArray) || gregorianStartDateArray.length !== 3 || !gregorianStartDateArray.every(num => typeof num === 'number')) {
             console.error("settingsService.toGregorian did not return a valid [year, month, day] array for start date:", gregorianStartDateArray);
             const err = new Error(`Failed to convert start date to Gregorian (invalid format).`);
             err.statusCode = 400;
             throw err;
        }
        if (!Array.isArray(gregorianEndDateArray) || gregorianEndDateArray.length !== 3 || !gregorianEndDateArray.every(num => typeof num === 'number')) {
             console.error("settingsService.toGregorian did not return a valid [year, month, day] array for end date:", gregorianEndDateArray);
             const err = new Error(`Failed to convert end date to Gregorian (invalid format).`);
             err.statusCode = 400;
             throw err;
        }
        // ---------------------------------------------------------


        startDate = new Date(gregorianStartDateArray);
        endDate = new Date(gregorianEndDateArray);
        if (startDate >= endDate) {
             const err = new Error(`Start date and time (${startDate.toLocaleString()}) should be before end date and time (${endDate.toLocaleString()}).`);
             err.statusCode = 400;
             throw err;
        }




    } else {
        // Not scheduled, ensure dates are null
        console.log("Posting is not scheduled. startDate and endDate will be null.");
        startDate = null;
        endDate = null;
    }

    // Prepare SQL request inputs
    const request = pool.request()
      .input('deviceId', data.deviceId)
      .input('filePath', data.filePath)
      .input('fileType', data.fileType)
      .input('priority', data.priority)
      .input('isScheduled', Number(data.isScheduled)) // Ensure it's a number for SQL
      .input('status', data.status)
      .input('userId', data.userId);

    let dateFields = '';
    let dateValues = '';

    // Only add startDate and endDate inputs if they are valid Date objects
    // This check ensures we only add them for scheduled postings that had valid inputs
    if (startDate instanceof Date && !isNaN(startDate.getTime()) && endDate instanceof Date && !isNaN(endDate.getTime())) {
        request.input('startDate', startDate); // Pass the Date object. The SQL driver handles conversion to SQL datetime/datetime2 based on the column type. Node.js Date represents UTC, which datetime2 stores directly.
        request.input('endDate', endDate);     // Pass the Date object
        dateFields = ', startDate, endDate';
        dateValues = ', @startDate, @endDate';
    }

    const query = `
      INSERT INTO tbl_screen_post
      (deviceId, userId,filePath, fileType, priority, isScheduled${dateFields}, status, createdAt, updatedAt)
      VALUES (@deviceId,@userId, @filePath, @fileType, @priority, @isScheduled${dateValues}, @status, GETDATE(), GETDATE())
    `;

    // Add logging for the final query before execution
    console.log("Executing SQL Query:", query);
    // console.log("SQL Parameters:", request.parameters); // Be cautious logging sensitive info, but useful for debugging inputs

    const result = await request.query(query);
    return result;
  }catch(err){
    console.log('Error in createScreenPost:', err);
  }
};


const updateScreenPost = async (id, data) => {
  const request = pool.request().input('id', id);
  let startDate;
  let endDate;
  if(data.yearStartDateModel != null && data.monthStartDateModel != null && data.dayStartDateModel != null && data.yearEndDateModel != null && data.monthEndDateModel != null && data.dayEndDateModel != null) {

  const gregorianStartDate = await settingsService.toGregorian(Number(data.yearStartDateModel), Number(data.monthStartDateModel), Number(data.dayStartDateModel));
  const gregorianEndDate =  await settingsService.toGregorian(Number(data.yearEndDateModel), Number(data.monthEndDateModel), Number(data.dayEndDateModel));
  
   startDate =   new Date(gregorianStartDate);
   endDate =   new Date(gregorianEndDate);
   if (startDate >= endDate) {
    const err = new Error(`Start date should be before end date`);
    err.statusCode = 400;
    throw err;
   }
  }

  


  let setClauses = [];

  // Dynamically add fields if they're provided (not null or undefined)
  if (data.deviceId != null) {
    request.input('deviceId', data.deviceId);
    setClauses.push('deviceId = @deviceId');
  }

  if (data.filePath != null) {
    request.input('filePath', data.filePath);
    setClauses.push('filePath = @filePath');
  }

  if (data.fileType != null) {
    request.input('fileType', data.fileType);
    setClauses.push('fileType = @fileType');
  }

  if (data.priority != null) {
    request.input('priority', data.priority);
    setClauses.push('priority = @priority');
  }

  if (data.isScheduled != null) {
    request.input('isScheduled', data.isScheduled);
    setClauses.push('isScheduled = @isScheduled');
  }

  if (data.status != null) {
    request.input('status', data.status);
    setClauses.push('status = @status');
  }

  if (startDate != null) {
    request.input('startDate', startDate);
    setClauses.push('startDate = @startDate');
  }

  if (endDate != null) {
    request.input('endDate', endDate);
    setClauses.push('endDate = @endDate');
  }

  // Always update the updatedAt field
  setClauses.push('updatedAt = GETDATE()');

  if (setClauses.length === 0) {
    throw new Error('No valid fields provided for update.');
  }

  

  const query = `
    UPDATE tbl_screen_post SET
      ${setClauses.join(',\n')}
    WHERE id = @id
  `;

  const result = await request.query(query);
   return result;
};




const deleteScreenPost = async (id) => {
  const result = await pool.request()
    .input('id', id)
    .query('DELETE FROM tbl_screen_post WHERE id = @id');
  return result;
};

const postToScreen = async (id) => {
  const result = await pool.request()
  .input('id', id)
  .query('update tbl_screen_post set status = 1 ,postedAt = GETDATE() WHERE id = @id');
  return result;
};

const removePost = async (id) => {
  const result = await pool.request()
  .input('id', id)
  .query('update tbl_screen_post set status = 0,isScheduled =0 WHERE id = @id');
  return result;
};




const updateScheduledPosts = async () => {
    try {
        // logger.debug('PostService: Running updateScheduledPosts DB queries...');

        // Ensure database connection is active before executing query
        const activePool = await ensureConnection();

        // Execute the batch of SQL queries
        const result = await activePool.request().query(`
            -- Update posts that should START now
            -- Check if current UTC time (GETUTCDATE()) is >= startDate
            -- And the post is currently in a scheduled/pending state (status = 0)
            UPDATE tbl_screen_post
            SET status = 1, postedAt = GETDATE(), updatedAt = GETDATE()
            WHERE isScheduled = 1
              AND GETUTCDATE() >= startDate
              AND status = 0;

            -- Update posts that should END now
            -- Check if current UTC time (GETUTCDATE()) is >= endDate
            -- And the post is currently in a posted state (status = 1)
            -- Note: Setting status to 0 might be ambiguous if 0 also means 'scheduled'.
            -- Consider adding a separate 'expired' status if possible in your schema.
            UPDATE tbl_screen_post
            SET status = 0, isScheduled = 0, updatedAt = GETDATE()
            WHERE isScheduled = 1
              AND GETUTCDATE() >= endDate
              AND status = 1;
        `);

        // Log the number of rows affected by each update statement
        // mssql's result.rowsAffected is an array for batched queries
        // The first element is for the first UPDATE, the second for the second UPDATE.
        const startedCount = result.rowsAffected && result.rowsAffected.length > 0 ? result.rowsAffected[0] : 0;
        const expiredCount = result.rowsAffected && result.rowsAffected.length > 1 ? result.rowsAffected[1] : 0;

        // if (startedCount > 0 || expiredCount > 0) {
        //     logger.info(`PostService: updateScheduledPosts completed. Posts started: ${startedCount}, Posts expired: ${expiredCount}.`);
        // } else {
        //     logger.debug('PostService: updateScheduledPosts completed. No posts needed status update.');
        // }

    } catch (err) {
        logger.error('PostService: Error updating scheduled posts:', err);

        // Check if it's a connection error and provide more specific logging
        if (err.message && err.message.includes('Connection is closed')) {
            logger.error('PostService: Database connection was closed. Reconnection attempt will be made on next cycle.');
        }

        // Re-throw the error so the checker knows it failed
        throw err;
    }
}

/**
 * Get active screen posts by device ID string
 * This function finds the device by its deviceId string, then gets all active posts for that device
 * @param {string} deviceIdString - The device ID string from the frontend
 * @returns {Array} - Array of screen posts with status=1
 */
const getActivePostsByDeviceIdString = async (deviceIdString) => {
  try {
    // Ensure database connection is active before executing queries
    const activePool = await ensureConnection();

    // First, find the device by its deviceId string
    const deviceResult = await activePool.request()
      .input('deviceId', deviceIdString)
      .query('SELECT id FROM tbl_devices WHERE deviceId = @deviceId');

    // If no device found, return empty array
    if (deviceResult.recordset.length === 0) {
      return [];
    }

    // Get the device ID (numeric)
    const deviceId = deviceResult.recordset[0].id;

    // Get all active posts for this device
    const result = await activePool.request()
      .input('deviceId', deviceId)
      .query(`
        SELECT
          id, filePath, fileType, priority,
          isScheduled, startDate, endDate, status,
          createdAt, postedAt
        FROM tbl_screen_post
        WHERE deviceId = @deviceId AND status = 1
        ORDER BY priority DESC, postedAt DESC
      `);

    return result.recordset;
  } catch (err) {
    logger.error('Error getting active posts by device ID string:', err);

    // Check if it's a connection error and provide more specific logging
    if (err.message && err.message.includes('Connection is closed')) {
      logger.error('Database connection was closed when getting active posts. Will attempt to reconnect on next request.');
    }

    // Re-throw to be handled by the caller
    throw err;
  }
};

module.exports = {
  getAllScreenPosts,
  getScreenPostById,
  createScreenPost,
  updateScreenPost,
  deleteScreenPost,
  postToScreen,
  removePost,
  getActivePostsByDeviceIdString,
  getScreenPostsByUserId,
  updateScheduledPosts,
  getAllActivePosts
};
