// socket.js - Socket.io functionality
const socketIo = require('socket.io');
let io;

// Initialize Socket.IO with server and options
const initialize = (server) => {
  io = socketIo(server, {
    cors: {
      origin: '*', // Allow all origins (Adjust this for production)
      methods: ['GET', 'POST'],
    },
  });

  // Handle Socket.IO connections
  io.on('connection', (socket) => {
   

    // Handle device registration
    socket.on('register-device', async (deviceId) => {
      try {
        if (!deviceId) {
          socket.emit('error', { message: 'Device ID is required' });
          return;
        }

        console.log(`Device registered: ${deviceId}`);
        socket.deviceId = deviceId; // Store deviceId in socket instance

        // Get active posts for this device
        const screenPostService = require('./services/screenPost.service');
        const posts = await screenPostService.getActivePostsByDeviceIdString(deviceId);

        // Get default settings for this device
        const defaultDeviceSettingsService = require('./services/defaultDeviceSettings.service');
        const settings = await defaultDeviceSettingsService.getSettingsByDeviceIdString(deviceId);

        // Send posts and settings to the device
        socket.emit('screen-posts', posts);
        socket.emit('device-settings', settings);
      } catch (error) {
        console.error('Error in register-device:', error);
        socket.emit('error', { message: 'Server error occurred' });
      }
    });

    // Request for refreshing posts
    socket.on('refresh-posts', async () => {
      try {
        if (!socket.deviceId) {
          socket.emit('error', { message: 'Device not registered' });
          return;
        }

        // Get active posts for this device
        const screenPostService = require('./services/screenPost.service');
        const posts = await screenPostService.getActivePostsByDeviceIdString(socket.deviceId);

        // Send posts to the device
        socket.emit('screen-posts', posts);
      } catch (error) {
        console.error('Error in refresh-posts:', error);
        socket.emit('error', { message: 'Server error occurred' });
      }
    });
    socket.on('refresh-office-posts', async () => {
      // This listener assumes the client is already an office app.
      // You could add a check: if (!socket.isOfficeApp) return;

      try {
        console.log(`Office App [${socket.id}] requested a manual refresh.`);

        // Call the service to get the latest list of all active posts
        const screenPostService = require('./services/screenPost.service');
        const posts = await screenPostService.getAllActivePosts();

        // Send the fresh list back to the specific client that asked for it.
        socket.emit('screen-posts', posts);

      } catch (error) {
        console.error('Error during refresh-office-posts:', error);
        // It's good practice to notify the client if an error occurs.
        socket.emit('error', { message: 'Server error during refresh.' });
      }
    });

    // Request for refreshing device settings
    socket.on('refresh-settings', async () => {
      try {
        if (!socket.deviceId) {
          socket.emit('error', { message: 'Device not registered' });
          return;
        }

        // Get default settings for this device
        const defaultDeviceSettingsService = require('./services/defaultDeviceSettings.service');
        const settings = await defaultDeviceSettingsService.getSettingsByDeviceIdString(socket.deviceId);

        // Send settings to the device
        socket.emit('device-settings', settings);
      } catch (error) {
        console.error('Error in refresh-settings:', error);
        socket.emit('error', { message: 'Server error occurred' });
      }
    });
	
	
 socket.on('register-office-app', async () => {
  try {
    
    socket.isOfficeApp = true;
    const screenPostService = require('./services/screenPost.service');
    // This now calls the function you just created!
    const posts = await screenPostService.getAllActivePosts();

    // Send all active posts to the newly connected office app
    socket.emit('screen-posts', posts);
  } catch (error) {
    console.error('Error in register-office-app:', error);
    socket.emit('error', { message: 'Server error occurred while fetching all posts' });
  }
});

    socket.on('disconnect', () => {
      console.log('Socket disconnected:', socket.id);
    });
  });

  return io;
};



// Function to broadcast updates to all connected devices
const broadcastToDevice = async (deviceId) => {
  try {
    if (!io) {
      console.error('Socket.io not initialized');
      return;
    }

    // Find all sockets with this deviceId
    const sockets = Array.from(io.sockets.sockets.values())
      .filter(s => s.deviceId === deviceId);

    if (sockets.length === 0) {
      console.log(`No connected devices found with ID ${deviceId}`);
      return;
    }

    // Get active posts for this device
    const screenPostService = require('./services/screenPost.service');
    const posts = await screenPostService.getActivePostsByDeviceIdString(deviceId);

    // Send to all connected instances of this device
    sockets.forEach(socket => {
      socket.emit('screen-posts', posts);
    });

    console.log(`Broadcast sent to ${sockets.length} instances of device ${deviceId}`);
  } catch (error) {
    console.error('Error in broadcastToDevice:', error);
  }
};

// Function to broadcast default settings to a specific device
const broadcastSettingsToDevice = async (deviceId) => {
  try {
    if (!io) {
      console.error('Socket.io not initialized');
      return;
    }

    // Find all sockets with this deviceId
    const sockets = Array.from(io.sockets.sockets.values())
      .filter(s => s.deviceId === deviceId);

    if (sockets.length === 0) {
      console.log(`No connected devices found with ID ${deviceId}`);
      return;
    }

    // Get default settings for this device
    const defaultDeviceSettingsService = require('./services/defaultDeviceSettings.service');
    const settings = await defaultDeviceSettingsService.getSettingsByDeviceIdString(deviceId);

    // Send to all connected instances of this device
    sockets.forEach(socket => {
      socket.emit('device-settings', settings);
    });

    console.log(`Settings broadcast sent to ${sockets.length} instances of device ${deviceId}`);
  } catch (error) {
    console.error('Error in broadcastSettingsToDevice:', error);
  }
};

const broadcastToOfficeApps = async (posts) => {
  try {
    if (!io) {
      console.error('Socket.io not initialized');
      return;
    }
    
    const officeSockets = Array.from(io.sockets.sockets.values())
      .filter(s => s.isOfficeApp === true);

    if (officeSockets.length === 0) {
      return; // No one to send to, just return silently.
    }
    
    // If posts weren't passed in, fetch them. Otherwise, use the provided list.
    const postsToSend = posts || await require('./services/screenPost.service').getAllActivePosts();

    officeSockets.forEach(socket => {
      socket.emit('screen-posts', postsToSend);
    });

    console.log(`Broadcast sent to ${officeSockets.length} office app(s).`);
  } catch (error) {
    console.error('Error in broadcastToOfficeApps:', error);
  }
};


module.exports = {
  initialize,
  broadcastToDevice,
  broadcastSettingsToDevice,
  broadcastToOfficeApps
};
