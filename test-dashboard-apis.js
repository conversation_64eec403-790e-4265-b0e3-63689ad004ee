// test-dashboard-apis.js
const axios = require('axios');
require('dotenv').config();

// Base URL for the API
const API_BASE_URL = 'http://localhost:3000/v1/api';

// Sample JWT token for authentication (you'll need to replace this with a valid token)
// You can get this by logging in through the login API
let TOKEN = '';

// Function to login and get a token
async function login() {
  try {
    const response = await axios.post(`${API_BASE_URL}/login`, {
      email: process.env.TEST_USER_EMAIL || '<EMAIL>',
      password: process.env.TEST_USER_PASSWORD || 'password123'
    });
    
    if (response.data && response.data.data && response.data.data.token) {
      TOKEN = response.data.data.token;
      console.log('Login successful. Token acquired.');
      return true;
    } else {
      console.error('Login failed. No token in response:', response.data);
      return false;
    }
  } catch (error) {
    console.error('Login error:', error.response ? error.response.data : error.message);
    return false;
  }
}

// Test functions for each dashboard API
async function testTotalDevices() {
  try {
    const response = await axios.get(`${API_BASE_URL}/dashboard/devices/total`, {
      headers: { Authorization: `Bearer ${TOKEN}` }
    });
    console.log('Total Devices API Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Total Devices API Error:', error.response ? error.response.data : error.message);
    return false;
  }
}

async function testDeviceStatusCounts() {
  try {
    const response = await axios.get(`${API_BASE_URL}/dashboard/devices/status`, {
      headers: { Authorization: `Bearer ${TOKEN}` }
    });
    console.log('Device Status Counts API Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Device Status Counts API Error:', error.response ? error.response.data : error.message);
    return false;
  }
}

async function testContentStatusCounts() {
  try {
    const response = await axios.get(`${API_BASE_URL}/dashboard/content/status`, {
      headers: { Authorization: `Bearer ${TOKEN}` }
    });
    console.log('Content Status Counts API Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Content Status Counts API Error:', error.response ? error.response.data : error.message);
    return false;
  }
}

async function testContentCountPerDevice() {
  try {
    const response = await axios.get(`${API_BASE_URL}/dashboard/content/per-device`, {
      headers: { Authorization: `Bearer ${TOKEN}` }
    });
    console.log('Content Count Per Device API Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Content Count Per Device API Error:', error.response ? error.response.data : error.message);
    return false;
  }
}

async function testContentTypeCountPerDevice() {
  try {
    const response = await axios.get(`${API_BASE_URL}/dashboard/content/types`, {
      headers: { Authorization: `Bearer ${TOKEN}` }
    });
    console.log('Content Type Count Per Device API Response:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error('Content Type Count Per Device API Error:', error.response ? error.response.data : error.message);
    return false;
  }
}

// Main function to run all tests
async function runTests() {
  console.log('Starting dashboard API tests...');
  
  // First login to get a token
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.error('Login failed. Cannot proceed with tests.');
    return;
  }
  
  // Run all tests
  console.log('\n1. Testing Total Devices API:');
  await testTotalDevices();
  
  console.log('\n2. Testing Device Status Counts API:');
  await testDeviceStatusCounts();
  
  console.log('\n3. Testing Content Status Counts API:');
  await testContentStatusCounts();
  
  console.log('\n4. Testing Content Count Per Device API:');
  await testContentCountPerDevice();
  
  console.log('\n5. Testing Content Type Count Per Device API:');
  await testContentTypeCountPerDevice();
  
  console.log('\nAll tests completed.');
}

// Run the tests
runTests().catch(error => {
  console.error('Test execution error:', error);
});
